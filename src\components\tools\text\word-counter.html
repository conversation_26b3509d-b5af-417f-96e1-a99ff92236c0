<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字数统计器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 手绘素描风格 */
        body {
            background: linear-gradient(135deg, #fef7cd 0%, #fde047 100%);
            font-family: 'Comic Sans MS', cursive, sans-serif;
        }
        
        .sketch-border {
            border: 2px solid #eab308;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            position: relative;
        }
        
        .sketch-border::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 1px dashed #facc15;
            border-radius: 14px;
            pointer-events: none;
        }
        
        .btn-sketch {
            background: linear-gradient(145deg, #eab308, #ca8a04);
            border: 2px solid #a16207;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-sketch:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(234, 179, 8, 0.3);
        }
        
        .btn-sketch:active {
            transform: translateY(0);
        }
        
        .textarea-sketch {
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            background: white;
            transition: border-color 0.3s ease;
            resize: vertical;
            min-height: 200px;
        }
        
        .textarea-sketch:focus {
            outline: none;
            border-color: #eab308;
            box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }
        
        .stat-card {
            background: linear-gradient(145deg, #fffbeb, #fef3c7);
            border: 2px solid #facc15;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(234, 179, 8, 0.2);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #eab308;
            font-family: 'Courier New', monospace;
        }
        
        .stat-label {
            font-size: 14px;
            color: #6b7280;
            margin-top: 8px;
            font-weight: 500;
        }
        
        .stat-description {
            font-size: 12px;
            color: #9ca3af;
            margin-top: 4px;
        }
        
        .error-text {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        .success-text {
            color: #059669;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        /* 装饰元素 */
        .doodle-star {
            position: absolute;
            color: #eab308;
            font-size: 20px;
            animation: sparkle 2s ease-in-out infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { opacity: 1; transform: scale(1) rotate(0deg); }
            50% { opacity: 0.7; transform: scale(1.1) rotate(180deg); }
        }
        
        .text-icon {
            stroke: #eab308;
            stroke-width: 2;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
            animation: flow 2s linear infinite;
        }
        
        @keyframes flow {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -20; }
        }
        
        .progress-bar {
            background: #f3f4f6;
            border-radius: 8px;
            height: 8px;
            overflow: hidden;
            margin-top: 8px;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #eab308, #facc15);
            height: 100%;
            border-radius: 8px;
            transition: width 0.3s ease;
        }
        
        .reading-time {
            background: white;
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            margin-top: 12px;
        }
        
        .reading-time-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .reading-time-item:last-child {
            border-bottom: none;
        }
        
        .reading-time-label {
            font-size: 14px;
            color: #374151;
        }
        
        .reading-time-value {
            font-size: 14px;
            font-weight: 600;
            color: #eab308;
        }
        
        .word-frequency {
            background: white;
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .word-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 8px;
            margin: 2px 0;
            background: #f9fafb;
            border-radius: 4px;
            font-size: 13px;
        }
        
        .word-text {
            font-family: 'Courier New', monospace;
            color: #374151;
        }
        
        .word-count {
            background: #eab308;
            color: white;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .analysis-section {
            background: white;
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 16px;
            margin-top: 12px;
        }
        
        .analysis-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
        }
        
        .analysis-item {
            text-align: center;
            padding: 8px;
            background: #f9fafb;
            border-radius: 6px;
        }
        
        .analysis-value {
            font-size: 18px;
            font-weight: bold;
            color: #eab308;
        }
        
        .analysis-label {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }
        
        .target-counter {
            background: linear-gradient(145deg, #dbeafe, #bfdbfe);
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 16px;
            margin-top: 12px;
        }
        
        .target-input {
            border: 2px solid #3b82f6;
            border-radius: 6px;
            padding: 8px 12px;
            background: white;
            width: 100px;
            text-align: center;
        }
        
        .target-input:focus {
            outline: none;
            border-color: #1d4ed8;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .target-progress {
            margin-top: 12px;
        }
        
        .target-progress-bar {
            background: #e5e7eb;
            border-radius: 8px;
            height: 12px;
            overflow: hidden;
        }
        
        .target-progress-fill {
            background: linear-gradient(90deg, #3b82f6, #60a5fa);
            height: 100%;
            border-radius: 8px;
            transition: width 0.3s ease;
        }
        
        .target-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            font-size: 14px;
        }
        
        .char-highlight {
            background: #fef3c7;
            padding: 2px 4px;
            border-radius: 3px;
            border: 1px solid #facc15;
        }
        
        .export-btn {
            background: linear-gradient(145deg, #10b981, #059669);
            border: 2px solid #047857;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            padding: 8px 16px;
            transition: all 0.3s ease;
            font-size: 14px;
            cursor: pointer;
        }
        
        .export-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
        }
    </style>
</head>
<body class="min-h-screen p-6">
    <div class="max-w-7xl mx-auto">
        <!-- 标题区域 -->
        <div class="text-center mb-8 relative">
            <div class="doodle-star" style="top: 10px; left: 15%;">📊</div>
            <div class="doodle-star" style="top: 15px; right: 20%; animation-delay: 1s;">📝</div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">字数统计器</h1>
            <p class="text-lg text-gray-600">全面分析文本的字数、词频、阅读时间等信息</p>
            
            <!-- 文本图标 -->
            <svg class="absolute top-16 left-1/2 transform -translate-x-1/2" width="80" height="40" viewBox="0 0 80 40">
                <text class="text-icon" x="10" y="25" font-family="monospace" font-size="14" stroke-dasharray="2,2">123</text>
                <text class="text-icon" x="40" y="25" font-family="monospace" font-size="14" stroke-dasharray="2,2" style="animation-delay: 0.5s;">ABC</text>
            </svg>
        </div>
        
        <!-- 输入区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 文本输入 -->
            <div class="lg:col-span-2">
                <div class="sketch-border bg-white p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="mr-2">📝</span>
                        输入文本
                    </h2>
                    
                    <textarea 
                        id="textInput" 
                        class="textarea-sketch w-full" 
                        placeholder="在此输入或粘贴要统计的文本..."
                        rows="12"
                    ></textarea>
                    
                    <div class="flex flex-wrap gap-3 mt-4">
                        <button id="clearBtn" class="btn-sketch">
                            <span class="mr-1">🗑️</span>
                            清空
                        </button>
                        <button id="pasteBtn" class="btn-sketch bg-gradient-to-r from-blue-500 to-blue-600 border-blue-700">
                            <span class="mr-1">📋</span>
                            粘贴
                        </button>
                        <button id="sampleBtn" class="btn-sketch bg-gradient-to-r from-green-500 to-green-600 border-green-700">
                            <span class="mr-1">📄</span>
                            示例文本
                        </button>
                        <button id="exportBtn" class="export-btn">
                            <span class="mr-1">📊</span>
                            导出报告
                        </button>
                    </div>
                </div>
                
                <!-- 目标计数器 -->
                <div class="target-counter">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <span class="mr-2">🎯</span>
                        目标计数器
                    </h3>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">目标字数：</label>
                            <input type="number" id="targetWords" class="target-input" value="500" min="1">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">目标字符数：</label>
                            <input type="number" id="targetChars" class="target-input" value="2000" min="1">
                        </div>
                    </div>
                    
                    <div class="target-progress">
                        <div class="text-sm font-medium text-gray-700 mb-2">字数进度：</div>
                        <div class="target-progress-bar">
                            <div id="wordProgress" class="target-progress-fill" style="width: 0%"></div>
                        </div>
                        <div class="target-status">
                            <span id="wordStatus">0 / 500 字</span>
                            <span id="wordPercentage">0%</span>
                        </div>
                    </div>
                    
                    <div class="target-progress">
                        <div class="text-sm font-medium text-gray-700 mb-2">字符进度：</div>
                        <div class="target-progress-bar">
                            <div id="charProgress" class="target-progress-fill" style="width: 0%"></div>
                        </div>
                        <div class="target-status">
                            <span id="charStatus">0 / 2000 字符</span>
                            <span id="charPercentage">0%</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="space-y-6">
                <!-- 基础统计 -->
                <div class="sketch-border bg-white p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="mr-2">📊</span>
                        基础统计
                    </h3>
                    
                    <div class="grid grid-cols-2 gap-3">
                        <div class="stat-card">
                            <div id="charCount" class="stat-number">0</div>
                            <div class="stat-label">字符数</div>
                            <div class="stat-description">包含空格</div>
                        </div>
                        
                        <div class="stat-card">
                            <div id="charCountNoSpaces" class="stat-number">0</div>
                            <div class="stat-label">字符数</div>
                            <div class="stat-description">不含空格</div>
                        </div>
                        
                        <div class="stat-card">
                            <div id="wordCount" class="stat-number">0</div>
                            <div class="stat-label">单词数</div>
                            <div class="stat-description">空格分隔</div>
                        </div>
                        
                        <div class="stat-card">
                            <div id="sentenceCount" class="stat-number">0</div>
                            <div class="stat-label">句子数</div>
                            <div class="stat-description">标点分隔</div>
                        </div>
                        
                        <div class="stat-card">
                            <div id="paragraphCount" class="stat-number">0</div>
                            <div class="stat-label">段落数</div>
                            <div class="stat-description">换行分隔</div>
                        </div>
                        
                        <div class="stat-card">
                            <div id="lineCount" class="stat-number">0</div>
                            <div class="stat-label">行数</div>
                            <div class="stat-description">总行数</div>
                        </div>
                    </div>
                </div>
                
                <!-- 阅读时间 -->
                <div class="sketch-border bg-white p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="mr-2">⏱️</span>
                        阅读时间
                    </h3>
                    
                    <div class="reading-time">
                        <div class="reading-time-item">
                            <span class="reading-time-label">慢速阅读 (150字/分)</span>
                            <span id="slowReading" class="reading-time-value">0分钟</span>
                        </div>
                        <div class="reading-time-item">
                            <span class="reading-time-label">正常阅读 (250字/分)</span>
                            <span id="normalReading" class="reading-time-value">0分钟</span>
                        </div>
                        <div class="reading-time-item">
                            <span class="reading-time-label">快速阅读 (400字/分)</span>
                            <span id="fastReading" class="reading-time-value">0分钟</span>
                        </div>
                        <div class="reading-time-item">
                            <span class="reading-time-label">演讲时间 (180字/分)</span>
                            <span id="speechTime" class="reading-time-value">0分钟</span>
                        </div>
                    </div>
                </div>
                
                <!-- 详细分析 -->
                <div class="analysis-section">
                    <div class="analysis-title">
                        <span class="mr-2">🔍</span>
                        详细分析
                    </div>
                    
                    <div class="analysis-grid">
                        <div class="analysis-item">
                            <div id="avgWordsPerSentence" class="analysis-value">0</div>
                            <div class="analysis-label">平均句长</div>
                        </div>
                        <div class="analysis-item">
                            <div id="avgCharsPerWord" class="analysis-value">0</div>
                            <div class="analysis-label">平均词长</div>
                        </div>
                        <div class="analysis-item">
                            <div id="longestWord" class="analysis-value">-</div>
                            <div class="analysis-label">最长单词</div>
                        </div>
                        <div class="analysis-item">
                            <div id="uniqueWords" class="analysis-value">0</div>
                            <div class="analysis-label">不重复词</div>
                        </div>
                        <div class="analysis-item">
                            <div id="chineseChars" class="analysis-value">0</div>
                            <div class="analysis-label">中文字符</div>
                        </div>
                        <div class="analysis-item">
                            <div id="englishWords" class="analysis-value">0</div>
                            <div class="analysis-label">英文单词</div>
                        </div>
                        <div class="analysis-item">
                            <div id="numbers" class="analysis-value">0</div>
                            <div class="analysis-label">数字</div>
                        </div>
                        <div class="analysis-item">
                            <div id="punctuation" class="analysis-value">0</div>
                            <div class="analysis-label">标点符号</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 词频分析 -->
        <div class="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="sketch-border bg-white p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">📈</span>
                    词频统计 (前20)
                </h3>
                
                <div id="wordFrequency" class="word-frequency">
                    <div class="text-center text-gray-500 py-8">输入文本后显示词频统计</div>
                </div>
            </div>
            
            <div class="sketch-border bg-white p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">🔤</span>
                    字符频率 (前20)
                </h3>
                
                <div id="charFrequency" class="word-frequency">
                    <div class="text-center text-gray-500 py-8">输入文本后显示字符频率</div>
                </div>
            </div>
        </div>
        
        <!-- 状态信息 -->
        <div id="statusInfo" class="mt-4"></div>
    </div>
    
    <script>
        class WordCounter {
            constructor() {
                this.initElements();
                this.bindEvents();
                this.sampleTexts = [
                    "这是一个示例文本，用于演示字数统计器的功能。它包含了中文和English混合的内容，以及一些数字123和标点符号！通过这个工具，您可以快速了解文本的各种统计信息，包括字数、词数、句子数、段落数等。同时还能分析阅读时间、词频分布等详细信息。",
                    "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
                    "人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。自诞生以来，理论和技术日益成熟，应用领域也不断扩大。"
                ];
            }
            
            initElements() {
                // 输入元素
                this.textInput = document.getElementById('textInput');
                this.targetWords = document.getElementById('targetWords');
                this.targetChars = document.getElementById('targetChars');
                
                // 按钮
                this.clearBtn = document.getElementById('clearBtn');
                this.pasteBtn = document.getElementById('pasteBtn');
                this.sampleBtn = document.getElementById('sampleBtn');
                this.exportBtn = document.getElementById('exportBtn');
                
                // 基础统计元素
                this.charCount = document.getElementById('charCount');
                this.charCountNoSpaces = document.getElementById('charCountNoSpaces');
                this.wordCount = document.getElementById('wordCount');
                this.sentenceCount = document.getElementById('sentenceCount');
                this.paragraphCount = document.getElementById('paragraphCount');
                this.lineCount = document.getElementById('lineCount');
                
                // 阅读时间元素
                this.slowReading = document.getElementById('slowReading');
                this.normalReading = document.getElementById('normalReading');
                this.fastReading = document.getElementById('fastReading');
                this.speechTime = document.getElementById('speechTime');
                
                // 详细分析元素
                this.avgWordsPerSentence = document.getElementById('avgWordsPerSentence');
                this.avgCharsPerWord = document.getElementById('avgCharsPerWord');
                this.longestWord = document.getElementById('longestWord');
                this.uniqueWords = document.getElementById('uniqueWords');
                this.chineseChars = document.getElementById('chineseChars');
                this.englishWords = document.getElementById('englishWords');
                this.numbers = document.getElementById('numbers');
                this.punctuation = document.getElementById('punctuation');
                
                // 目标进度元素
                this.wordProgress = document.getElementById('wordProgress');
                this.charProgress = document.getElementById('charProgress');
                this.wordStatus = document.getElementById('wordStatus');
                this.charStatus = document.getElementById('charStatus');
                this.wordPercentage = document.getElementById('wordPercentage');
                this.charPercentage = document.getElementById('charPercentage');
                
                // 频率分析元素
                this.wordFrequency = document.getElementById('wordFrequency');
                this.charFrequency = document.getElementById('charFrequency');
                
                this.statusInfo = document.getElementById('statusInfo');
            }
            
            bindEvents() {
                // 输入监听
                this.textInput.addEventListener('input', () => {
                    this.updateAllStats();
                });
                
                // 目标设置监听
                this.targetWords.addEventListener('input', () => {
                    this.updateTargetProgress();
                });
                
                this.targetChars.addEventListener('input', () => {
                    this.updateTargetProgress();
                });
                
                // 按钮事件
                this.clearBtn.addEventListener('click', () => this.clearText());
                this.pasteBtn.addEventListener('click', () => this.pasteText());
                this.sampleBtn.addEventListener('click', () => this.loadSample());
                this.exportBtn.addEventListener('click', () => this.exportReport());
            }
            
            updateAllStats() {
                const text = this.textInput.value;
                
                // 基础统计
                this.updateBasicStats(text);
                
                // 阅读时间
                this.updateReadingTime(text);
                
                // 详细分析
                this.updateDetailedAnalysis(text);
                
                // 目标进度
                this.updateTargetProgress();
                
                // 频率分析
                this.updateFrequencyAnalysis(text);
            }
            
            updateBasicStats(text) {
                // 字符数（含空格）
                const charCountWithSpaces = text.length;
                this.charCount.textContent = charCountWithSpaces.toLocaleString();
                
                // 字符数（不含空格）
                const charCountWithoutSpaces = text.replace(/\s/g, '').length;
                this.charCountNoSpaces.textContent = charCountWithoutSpaces.toLocaleString();
                
                // 单词数
                const words = text.trim() ? text.trim().split(/\s+/) : [];
                const wordCount = words.length;
                this.wordCount.textContent = wordCount.toLocaleString();
                
                // 句子数
                const sentences = text.trim() ? text.split(/[.!?。！？]+/).filter(s => s.trim()) : [];
                this.sentenceCount.textContent = sentences.length.toLocaleString();
                
                // 段落数
                const paragraphs = text.trim() ? text.split(/\n\s*\n/).filter(p => p.trim()) : [];
                this.paragraphCount.textContent = paragraphs.length.toLocaleString();
                
                // 行数
                const lines = text ? text.split('\n') : [''];
                this.lineCount.textContent = lines.length.toLocaleString();
            }
            
            updateReadingTime(text) {
                const wordCount = text.trim() ? text.trim().split(/\s+/).length : 0;
                
                // 不同阅读速度的时间计算
                const slowTime = Math.ceil(wordCount / 150);
                const normalTime = Math.ceil(wordCount / 250);
                const fastTime = Math.ceil(wordCount / 400);
                const speechTime = Math.ceil(wordCount / 180);
                
                this.slowReading.textContent = this.formatTime(slowTime);
                this.normalReading.textContent = this.formatTime(normalTime);
                this.fastReading.textContent = this.formatTime(fastTime);
                this.speechTime.textContent = this.formatTime(speechTime);
            }
            
            formatTime(minutes) {
                if (minutes === 0) return '0分钟';
                if (minutes < 60) return `${minutes}分钟`;
                
                const hours = Math.floor(minutes / 60);
                const remainingMinutes = minutes % 60;
                
                if (remainingMinutes === 0) {
                    return `${hours}小时`;
                } else {
                    return `${hours}小时${remainingMinutes}分钟`;
                }
            }
            
            updateDetailedAnalysis(text) {
                const words = text.trim() ? text.trim().split(/\s+/) : [];
                const sentences = text.trim() ? text.split(/[.!?。！？]+/).filter(s => s.trim()) : [];
                
                // 平均句长
                const avgWordsPerSentence = sentences.length > 0 ? Math.round(words.length / sentences.length) : 0;
                this.avgWordsPerSentence.textContent = avgWordsPerSentence;
                
                // 平均词长
                const totalChars = words.join('').length;
                const avgCharsPerWord = words.length > 0 ? Math.round(totalChars / words.length) : 0;
                this.avgCharsPerWord.textContent = avgCharsPerWord;
                
                // 最长单词
                const longestWord = words.reduce((longest, word) => 
                    word.length > longest.length ? word : longest, '');
                this.longestWord.textContent = longestWord || '-';
                
                // 不重复词数
                const uniqueWords = new Set(words.map(word => word.toLowerCase())).size;
                this.uniqueWords.textContent = uniqueWords.toLocaleString();
                
                // 中文字符数
                const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
                this.chineseChars.textContent = chineseChars.toLocaleString();
                
                // 英文单词数
                const englishWords = (text.match(/\b[a-zA-Z]+\b/g) || []).length;
                this.englishWords.textContent = englishWords.toLocaleString();
                
                // 数字数量
                const numbers = (text.match(/\d/g) || []).length;
                this.numbers.textContent = numbers.toLocaleString();
                
                // 标点符号数量
                const punctuation = (text.match(/[^\w\s\u4e00-\u9fff]/g) || []).length;
                this.punctuation.textContent = punctuation.toLocaleString();
            }
            
            updateTargetProgress() {
                const text = this.textInput.value;
                const currentWords = text.trim() ? text.trim().split(/\s+/).length : 0;
                const currentChars = text.length;
                
                const targetWords = parseInt(this.targetWords.value) || 1;
                const targetChars = parseInt(this.targetChars.value) || 1;
                
                // 字数进度
                const wordProgress = Math.min((currentWords / targetWords) * 100, 100);
                this.wordProgress.style.width = `${wordProgress}%`;
                this.wordStatus.textContent = `${currentWords.toLocaleString()} / ${targetWords.toLocaleString()} 字`;
                this.wordPercentage.textContent = `${Math.round(wordProgress)}%`;
                
                // 字符进度
                const charProgress = Math.min((currentChars / targetChars) * 100, 100);
                this.charProgress.style.width = `${charProgress}%`;
                this.charStatus.textContent = `${currentChars.toLocaleString()} / ${targetChars.toLocaleString()} 字符`;
                this.charPercentage.textContent = `${Math.round(charProgress)}%`;
            }
            
            updateFrequencyAnalysis(text) {
                if (!text.trim()) {
                    this.wordFrequency.innerHTML = '<div class="text-center text-gray-500 py-8">输入文本后显示词频统计</div>';
                    this.charFrequency.innerHTML = '<div class="text-center text-gray-500 py-8">输入文本后显示字符频率</div>';
                    return;
                }
                
                // 词频统计
                this.updateWordFrequency(text);
                
                // 字符频率统计
                this.updateCharFrequency(text);
            }
            
            updateWordFrequency(text) {
                const words = text.toLowerCase().match(/\b\w+\b/g) || [];
                const frequency = {};
                
                words.forEach(word => {
                    if (word.length > 1) { // 忽略单字符
                        frequency[word] = (frequency[word] || 0) + 1;
                    }
                });
                
                const sortedWords = Object.entries(frequency)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 20);
                
                if (sortedWords.length === 0) {
                    this.wordFrequency.innerHTML = '<div class="text-center text-gray-500 py-8">未找到有效单词</div>';
                    return;
                }
                
                this.wordFrequency.innerHTML = sortedWords.map(([word, count]) => `
                    <div class="word-item">
                        <span class="word-text">${word}</span>
                        <span class="word-count">${count}</span>
                    </div>
                `).join('');
            }
            
            updateCharFrequency(text) {
                const chars = text.replace(/\s/g, '').split('');
                const frequency = {};
                
                chars.forEach(char => {
                    if (char.trim()) {
                        frequency[char] = (frequency[char] || 0) + 1;
                    }
                });
                
                const sortedChars = Object.entries(frequency)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 20);
                
                if (sortedChars.length === 0) {
                    this.charFrequency.innerHTML = '<div class="text-center text-gray-500 py-8">未找到有效字符</div>';
                    return;
                }
                
                this.charFrequency.innerHTML = sortedChars.map(([char, count]) => `
                    <div class="word-item">
                        <span class="word-text">${char}</span>
                        <span class="word-count">${count}</span>
                    </div>
                `).join('');
            }
            
            // 工具方法
            clearText() {
                this.textInput.value = '';
                this.updateAllStats();
                this.showStatus('已清空文本', 'success');
            }
            
            async pasteText() {
                try {
                    const text = await navigator.clipboard.readText();
                    this.textInput.value = text;
                    this.updateAllStats();
                    this.showStatus('已粘贴文本', 'success');
                } catch (error) {
                    this.showStatus('粘贴失败，请手动粘贴', 'error');
                }
            }
            
            loadSample() {
                const randomIndex = Math.floor(Math.random() * this.sampleTexts.length);
                this.textInput.value = this.sampleTexts[randomIndex];
                this.updateAllStats();
                this.showStatus('已加载示例文本', 'success');
            }
            
            exportReport() {
                const text = this.textInput.value;
                if (!text.trim()) {
                    this.showStatus('请先输入文本', 'error');
                    return;
                }
                
                const report = this.generateReport(text);
                this.downloadReport(report);
                this.showStatus('报告已导出', 'success');
            }
            
            generateReport(text) {
                const words = text.trim() ? text.trim().split(/\s+/) : [];
                const sentences = text.trim() ? text.split(/[.!?。！？]+/).filter(s => s.trim()) : [];
                const paragraphs = text.trim() ? text.split(/\n\s*\n/).filter(p => p.trim()) : [];
                
                return `文本统计报告
===================

基础统计:
- 字符数（含空格）: ${text.length.toLocaleString()}
- 字符数（不含空格）: ${text.replace(/\s/g, '').length.toLocaleString()}
- 单词数: ${words.length.toLocaleString()}
- 句子数: ${sentences.length.toLocaleString()}
- 段落数: ${paragraphs.length.toLocaleString()}
- 行数: ${text.split('\n').length.toLocaleString()}

阅读时间:
- 慢速阅读: ${this.formatTime(Math.ceil(words.length / 150))}
- 正常阅读: ${this.formatTime(Math.ceil(words.length / 250))}
- 快速阅读: ${this.formatTime(Math.ceil(words.length / 400))}
- 演讲时间: ${this.formatTime(Math.ceil(words.length / 180))}

详细分析:
- 平均句长: ${sentences.length > 0 ? Math.round(words.length / sentences.length) : 0} 字
- 平均词长: ${words.length > 0 ? Math.round(words.join('').length / words.length) : 0} 字符
- 最长单词: ${words.reduce((longest, word) => word.length > longest.length ? word : longest, '') || '-'}
- 不重复词数: ${new Set(words.map(word => word.toLowerCase())).size.toLocaleString()}
- 中文字符: ${(text.match(/[\u4e00-\u9fff]/g) || []).length.toLocaleString()}
- 英文单词: ${(text.match(/\b[a-zA-Z]+\b/g) || []).length.toLocaleString()}
- 数字: ${(text.match(/\d/g) || []).length.toLocaleString()}
- 标点符号: ${(text.match(/[^\w\s\u4e00-\u9fff]/g) || []).length.toLocaleString()}

生成时间: ${new Date().toLocaleString()}`;
            }
            
            downloadReport(content) {
                const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `文本统计报告_${new Date().toISOString().slice(0, 10)}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
            
            showStatus(message, type) {
                const className = type === 'error' ? 'error-text' : 'success-text';
                this.statusInfo.innerHTML = `<div class="${className}">${message}</div>`;
                
                // 3秒后自动清除状态
                setTimeout(() => {
                    this.statusInfo.innerHTML = '';
                }, 3000);
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new WordCounter();
        });
    </script>
</body>
</html>