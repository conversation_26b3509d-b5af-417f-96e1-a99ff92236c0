**目标**：使用HTML、CSS、JavaScript创建一个现代化、响应式的AI工具集网站，采用卡片式布局，提供良好的用户体验。

## 📋 核心功能要求
1. **工具展示**：以卡片形式展示各类AI工具
2. **分类管理**：左侧垂直导航，支持工具分类浏览
3. **搜索功能**：顶部搜索框，支持工具名称和描述搜索
4. **主题切换**：明暗模式切换功能
5. **响应式设计**：适配桌面端、平板和移动端

## 🎨 视觉设计规范

### 配色方案
```css
/* 主色调 */
--primary-color: #5B6AFF;     /* 蓝紫色 */
--secondary-color: #8C54FF;   /* 紫色 */
--accent-color: #667EEA;      /* 渐变辅助色 */

/* 背景色 */
--bg-primary: #FFFFFF;        /* 主背景 */
--bg-secondary: #F5F7FA;      /* 次要背景 */
--bg-card: #FFFFFF;           /* 卡片背景 */

/* 文字颜色 */
--text-primary: #333333;      /* 主文字 */
--text-secondary: #666666;    /* 次要文字 */
--text-muted: #999999;        /* 辅助文字 */
```

### 设计元素
- **字体**：系统字体栈，优先使用 -apple-system, BlinkMacSystemFont, 'Segoe UI'
- **圆角**：统一使用 12px
- **阴影**：`box-shadow: 0 4px 12px rgba(0,0,0,0.08)`
- **间距**：8px 基础单位（8px, 16px, 24px, 32px）
- **动画**：使用 CSS transition，时长 0.3s，缓动函数 ease

## 🏗️ 布局结构

### 整体布局（CSS Grid）
```
┌─────────────────────────────────────┐
│           顶部导航栏 (60px)          │
├──────────┬──────────────────────────┤
│   左侧   │                          │
│   导航   │        主内容区           │
│ (240px)  │                          │
│          │                          │
└──────────┴──────────────────────────┘
```

### 1. 顶部导航栏
**必须包含**：
- Logo + 网站标题
- 全局搜索框（居中）
- 主题切换按钮
- 用户菜单（可选）

### 2. 左侧分类导航
**功能要求**：
- 可收缩展开
- 图标 + 文字组合
- 当前分类高亮
- 分类数量显示
- 移动端自动隐藏

### 3. 主内容区
**布局方式**：
- CSS Grid 响应式布局
- 桌面端：3-4列
- 平板端：2列  
- 移动端：1列

## 🃏 工具卡片设计

### 卡片内容结构
```html
<div class="tool-card">
  <div class="card-icon"><!-- 工具图标 --></div>
  <h3 class="card-title"><!-- 工具名称 --></h3>
  <p class="card-description"><!-- 简短描述 --></p>
  <div class="card-tags"><!-- 标签 --></div>
  <button class="card-action">使用工具</button>
</div>
```

### 交互效果
- **悬停**：卡片上浮 4px，阴影加深
- **点击**：轻微缩放效果
- **加载**：骨架屏动画

## 📱 响应式断点
```css
/* 移动端 */
@media (max-width: 768px) { /* 单列布局，隐藏侧边栏 */ }

/* 平板端 */
@media (min-width: 769px) and (max-width: 1024px) { /* 双列布局 */ }

/* 桌面端 */
@media (min-width: 1025px) { /* 多列布局 */ }
```

## ⚡ 技术实现要点

### JavaScript 功能模块
1. **搜索功能**：实时过滤，支持拼音搜索
2. **主题切换**：localStorage 持久化
3. **分类筛选**：URL 路由支持
4. **懒加载**：工具卡片懒加载优化
5. **无障碍**：键盘导航支持

### 性能优化
- CSS 使用 CSS Custom Properties
- JavaScript 使用现代 ES6+ 语法
- 图片使用 WebP 格式，fallback PNG
- 关键 CSS 内联，非关键 CSS 异步加载