<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码生成器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        /* 手绘素描风格 */
        body {
            background: linear-gradient(135deg, #fef7cd 0%, #fde047 100%);
            font-family: 'Comic Sans MS', cursive, sans-serif;
        }
        
        .sketch-border {
            border: 2px solid #eab308;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            position: relative;
        }
        
        .sketch-border::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 1px dashed #facc15;
            border-radius: 14px;
            pointer-events: none;
        }
        
        .btn-sketch {
            background: linear-gradient(145deg, #eab308, #ca8a04);
            border: 2px solid #a16207;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-sketch:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(234, 179, 8, 0.3);
        }
        
        .btn-sketch:active {
            transform: translateY(0);
        }
        
        .input-sketch {
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            background: white;
            transition: border-color 0.3s ease;
        }
        
        .input-sketch:focus {
            outline: none;
            border-color: #eab308;
            box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }
        
        .select-sketch {
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            background: white;
            transition: border-color 0.3s ease;
        }
        
        .select-sketch:focus {
            outline: none;
            border-color: #eab308;
            box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }
        
        .qr-preview {
            background: white;
            border: 3px solid #eab308;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .qr-placeholder {
            color: #9ca3af;
            font-size: 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
        }
        
        .qr-code-container {
            position: relative;
            display: inline-block;
        }
        
        .qr-code-canvas {
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .download-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .qr-code-container:hover .download-overlay {
            opacity: 1;
        }
        
        .download-btn {
            background: white;
            color: #374151;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            background: #f3f4f6;
            transform: scale(1.05);
        }
        
        .error-text {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        .success-text {
            color: #059669;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        /* 装饰元素 */
        .doodle-star {
            position: absolute;
            color: #eab308;
            font-size: 20px;
            animation: sparkle 2s ease-in-out infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { opacity: 1; transform: scale(1) rotate(0deg); }
            50% { opacity: 0.7; transform: scale(1.1) rotate(180deg); }
        }
        
        .qr-icon {
            stroke: #eab308;
            stroke-width: 2;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
            animation: scan 2s linear infinite;
        }
        
        @keyframes scan {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -40; }
        }
        
        .preset-btn {
            background: linear-gradient(145deg, #3b82f6, #2563eb);
            border: 2px solid #1d4ed8;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            padding: 8px 16px;
            transition: all 0.3s ease;
            font-size: 14px;
            cursor: pointer;
        }
        
        .preset-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        }
        
        .color-input {
            width: 50px;
            height: 40px;
            border: 2px solid #facc15;
            border-radius: 6px;
            cursor: pointer;
            background: none;
        }
        
        .color-input::-webkit-color-swatch {
            border: none;
            border-radius: 4px;
        }
        
        .color-input::-webkit-color-swatch-wrapper {
            padding: 0;
            border: none;
            border-radius: 4px;
        }
        
        .size-slider {
            -webkit-appearance: none;
            appearance: none;
            height: 6px;
            border-radius: 3px;
            background: #e5e7eb;
            outline: none;
        }
        
        .size-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #eab308;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .size-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #eab308;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .history-item {
            background: white;
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .history-item:hover {
            border-color: #eab308;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(234, 179, 8, 0.2);
        }
        
        .history-text {
            font-size: 14px;
            color: #374151;
            margin-bottom: 4px;
            word-break: break-all;
        }
        
        .history-time {
            font-size: 12px;
            color: #9ca3af;
        }
        
        .batch-input {
            background: white;
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            min-height: 120px;
            resize: vertical;
        }
        
        .batch-input:focus {
            outline: none;
            border-color: #eab308;
            box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }
        
        .batch-result {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        
        .batch-qr-item {
            background: white;
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }
        
        .batch-qr-canvas {
            width: 100%;
            height: auto;
            border-radius: 4px;
            margin-bottom: 8px;
        }
        
        .batch-qr-text {
            font-size: 12px;
            color: #374151;
            word-break: break-all;
            margin-bottom: 8px;
        }
        
        .batch-download-btn {
            background: #eab308;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .batch-download-btn:hover {
            background: #ca8a04;
        }
    </style>
</head>
<body class="min-h-screen p-6">
    <div class="max-w-6xl mx-auto">
        <!-- 标题区域 -->
        <div class="text-center mb-8 relative">
            <div class="doodle-star" style="top: 10px; left: 15%;">📱</div>
            <div class="doodle-star" style="top: 15px; right: 20%; animation-delay: 1s;">🔗</div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">二维码生成器</h1>
            <p class="text-lg text-gray-600">快速生成高质量的二维码，支持多种格式和自定义样式</p>
            
            <!-- 二维码图标 -->
            <svg class="absolute top-16 left-1/2 transform -translate-x-1/2" width="60" height="60" viewBox="0 0 60 60">
                <rect class="qr-icon" x="5" y="5" width="15" height="15" stroke-dasharray="2,2"/>
                <rect class="qr-icon" x="40" y="5" width="15" height="15" stroke-dasharray="2,2" style="animation-delay: 0.3s;"/>
                <rect class="qr-icon" x="5" y="40" width="15" height="15" stroke-dasharray="2,2" style="animation-delay: 0.6s;"/>
                <rect class="qr-icon" x="25" y="25" width="10" height="10" stroke-dasharray="1,1" style="animation-delay: 0.9s;"/>
            </svg>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 输入和设置区域 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 基本设置 -->
                <div class="sketch-border bg-white p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="mr-2">⚙️</span>
                        基本设置
                    </h2>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">输入内容：</label>
                            <textarea 
                                id="qrText" 
                                class="input-sketch w-full" 
                                placeholder="输入要生成二维码的文本、网址、电话号码等..."
                                rows="3"
                            ></textarea>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">二维码大小：</label>
                                <div class="flex items-center space-x-3">
                                    <input 
                                        type="range" 
                                        id="qrSize" 
                                        class="size-slider flex-1" 
                                        min="100" 
                                        max="500" 
                                        value="200"
                                    >
                                    <span id="sizeValue" class="text-sm font-medium text-gray-600 w-12">200px</span>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">容错级别：</label>
                                <select id="errorLevel" class="select-sketch w-full">
                                    <option value="L">低 (7%)</option>
                                    <option value="M" selected>中 (15%)</option>
                                    <option value="Q">较高 (25%)</option>
                                    <option value="H">高 (30%)</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">输出格式：</label>
                                <select id="outputFormat" class="select-sketch w-full">
                                    <option value="png">PNG</option>
                                    <option value="jpeg">JPEG</option>
                                    <option value="svg">SVG</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">前景色：</label>
                                <div class="flex items-center space-x-3">
                                    <input type="color" id="foregroundColor" class="color-input" value="#000000">
                                    <span class="text-sm text-gray-600">二维码颜色</span>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">背景色：</label>
                                <div class="flex items-center space-x-3">
                                    <input type="color" id="backgroundColor" class="color-input" value="#ffffff">
                                    <span class="text-sm text-gray-600">背景颜色</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 快速预设 -->
                <div class="sketch-border bg-white p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="mr-2">🚀</span>
                        快速预设
                    </h3>
                    
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <button class="preset-btn" data-preset="website">
                            <span class="mr-1">🌐</span>
                            网站链接
                        </button>
                        <button class="preset-btn" data-preset="email">
                            <span class="mr-1">📧</span>
                            邮箱地址
                        </button>
                        <button class="preset-btn" data-preset="phone">
                            <span class="mr-1">📞</span>
                            电话号码
                        </button>
                        <button class="preset-btn" data-preset="sms">
                            <span class="mr-1">💬</span>
                            短信内容
                        </button>
                        <button class="preset-btn" data-preset="wifi">
                            <span class="mr-1">📶</span>
                            WiFi信息
                        </button>
                        <button class="preset-btn" data-preset="vcard">
                            <span class="mr-1">👤</span>
                            联系人卡片
                        </button>
                        <button class="preset-btn" data-preset="location">
                            <span class="mr-1">📍</span>
                            地理位置
                        </button>
                        <button class="preset-btn" data-preset="text">
                            <span class="mr-1">📝</span>
                            纯文本
                        </button>
                    </div>
                </div>
                
                <!-- 批量生成 -->
                <div class="sketch-border bg-white p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="mr-2">📦</span>
                        批量生成
                    </h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">批量内容（每行一个）：</label>
                            <textarea 
                                id="batchText" 
                                class="batch-input w-full" 
                                placeholder="输入多个内容，每行一个：\nhttps://example1.com\nhttps://example2.com\n联系电话：123456789"
                                rows="5"
                            ></textarea>
                        </div>
                        
                        <div class="flex flex-wrap gap-3">
                            <button id="generateBatchBtn" class="btn-sketch">
                                <span class="mr-1">🔄</span>
                                批量生成
                            </button>
                            <button id="downloadAllBtn" class="btn-sketch bg-gradient-to-r from-green-500 to-green-600 border-green-700">
                                <span class="mr-1">📥</span>
                                下载全部
                            </button>
                            <button id="clearBatchBtn" class="btn-sketch bg-gradient-to-r from-red-500 to-red-600 border-red-700">
                                <span class="mr-1">🗑️</span>
                                清空
                            </button>
                        </div>
                    </div>
                    
                    <div id="batchResult" class="batch-result"></div>
                </div>
            </div>
            
            <!-- 预览和历史区域 -->
            <div class="space-y-6">
                <!-- 二维码预览 -->
                <div class="sketch-border bg-white p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="mr-2">👁️</span>
                        预览
                    </h3>
                    
                    <div id="qrPreview" class="qr-preview">
                        <div class="qr-placeholder">
                            <svg width="80" height="80" viewBox="0 0 80 80" fill="none">
                                <rect x="10" y="10" width="20" height="20" stroke="#d1d5db" stroke-width="2" fill="none"/>
                                <rect x="50" y="10" width="20" height="20" stroke="#d1d5db" stroke-width="2" fill="none"/>
                                <rect x="10" y="50" width="20" height="20" stroke="#d1d5db" stroke-width="2" fill="none"/>
                                <rect x="35" y="35" width="10" height="10" stroke="#d1d5db" stroke-width="2" fill="none"/>
                            </svg>
                            <span>输入内容后自动生成二维码</span>
                        </div>
                    </div>
                    
                    <div class="flex flex-wrap gap-3 mt-4">
                        <button id="generateBtn" class="btn-sketch w-full">
                            <span class="mr-1">🔄</span>
                            生成二维码
                        </button>
                        <button id="downloadBtn" class="btn-sketch bg-gradient-to-r from-green-500 to-green-600 border-green-700 flex-1">
                            <span class="mr-1">📥</span>
                            下载
                        </button>
                        <button id="copyBtn" class="btn-sketch bg-gradient-to-r from-blue-500 to-blue-600 border-blue-700 flex-1">
                            <span class="mr-1">📋</span>
                            复制
                        </button>
                    </div>
                </div>
                
                <!-- 生成历史 -->
                <div class="sketch-border bg-white p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="mr-2">📚</span>
                        生成历史
                        <button id="clearHistoryBtn" class="ml-auto text-sm text-red-600 hover:text-red-800">
                            清空历史
                        </button>
                    </h3>
                    
                    <div id="qrHistory" class="max-h-64 overflow-y-auto">
                        <div class="text-center text-gray-500 py-8">暂无生成历史</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 状态信息 -->
        <div id="statusInfo" class="mt-4"></div>
    </div>
    
    <script>
        class QRGenerator {
            constructor() {
                this.initElements();
                this.bindEvents();
                this.history = JSON.parse(localStorage.getItem('qrHistory') || '[]');
                this.loadHistory();
                this.currentQRData = null;
                this.batchQRs = [];
                
                this.presets = {
                    website: 'https://example.com',
                    email: 'mailto:<EMAIL>',
                    phone: 'tel:+86-123-4567-8901',
                    sms: 'sms:+86-123-4567-8901?body=Hello',
                    wifi: 'WIFI:T:WPA;S:MyNetwork;P:MyPassword;;',
                    vcard: 'BEGIN:VCARD\nVERSION:3.0\nFN:张三\nORG:公司名称\nTEL:+86-123-4567-8901\nEMAIL:<EMAIL>\nEND:VCARD',
                    location: 'geo:39.9042,116.4074?q=北京天安门',
                    text: '这是一个示例文本'
                };
            }
            
            initElements() {
                // 输入元素
                this.qrText = document.getElementById('qrText');
                this.qrSize = document.getElementById('qrSize');
                this.errorLevel = document.getElementById('errorLevel');
                this.outputFormat = document.getElementById('outputFormat');
                this.foregroundColor = document.getElementById('foregroundColor');
                this.backgroundColor = document.getElementById('backgroundColor');
                this.batchText = document.getElementById('batchText');
                
                // 显示元素
                this.sizeValue = document.getElementById('sizeValue');
                this.qrPreview = document.getElementById('qrPreview');
                this.qrHistory = document.getElementById('qrHistory');
                this.batchResult = document.getElementById('batchResult');
                this.statusInfo = document.getElementById('statusInfo');
                
                // 按钮
                this.generateBtn = document.getElementById('generateBtn');
                this.downloadBtn = document.getElementById('downloadBtn');
                this.copyBtn = document.getElementById('copyBtn');
                this.generateBatchBtn = document.getElementById('generateBatchBtn');
                this.downloadAllBtn = document.getElementById('downloadAllBtn');
                this.clearBatchBtn = document.getElementById('clearBatchBtn');
                this.clearHistoryBtn = document.getElementById('clearHistoryBtn');
                
                // 预设按钮
                this.presetBtns = document.querySelectorAll('[data-preset]');
            }
            
            bindEvents() {
                // 输入监听
                this.qrText.addEventListener('input', () => {
                    if (this.qrText.value.trim()) {
                        this.generateQR();
                    } else {
                        this.showPlaceholder();
                    }
                });
                
                // 设置变化监听
                this.qrSize.addEventListener('input', () => {
                    this.sizeValue.textContent = this.qrSize.value + 'px';
                    if (this.qrText.value.trim()) {
                        this.generateQR();
                    }
                });
                
                [this.errorLevel, this.outputFormat, this.foregroundColor, this.backgroundColor].forEach(element => {
                    element.addEventListener('change', () => {
                        if (this.qrText.value.trim()) {
                            this.generateQR();
                        }
                    });
                });
                
                // 按钮事件
                this.generateBtn.addEventListener('click', () => this.generateQR());
                this.downloadBtn.addEventListener('click', () => this.downloadQR());
                this.copyBtn.addEventListener('click', () => this.copyQR());
                this.generateBatchBtn.addEventListener('click', () => this.generateBatch());
                this.downloadAllBtn.addEventListener('click', () => this.downloadAllBatch());
                this.clearBatchBtn.addEventListener('click', () => this.clearBatch());
                this.clearHistoryBtn.addEventListener('click', () => this.clearHistory());
                
                // 预设按钮事件
                this.presetBtns.forEach(btn => {
                    btn.addEventListener('click', () => {
                        const preset = btn.dataset.preset;
                        this.qrText.value = this.presets[preset];
                        this.generateQR();
                        this.showStatus(`已加载${btn.textContent.trim()}预设`, 'success');
                    });
                });
            }
            
            async generateQR() {
                const text = this.qrText.value.trim();
                if (!text) {
                    this.showPlaceholder();
                    return;
                }
                
                try {
                    const options = {
                        errorCorrectionLevel: this.errorLevel.value,
                        type: 'image/png',
                        quality: 0.92,
                        margin: 1,
                        color: {
                            dark: this.foregroundColor.value,
                            light: this.backgroundColor.value
                        },
                        width: parseInt(this.qrSize.value)
                    };
                    
                    if (this.outputFormat.value === 'svg') {
                        const svgString = await QRCode.toString(text, {
                            ...options,
                            type: 'svg'
                        });
                        this.displaySVGQR(svgString, text);
                    } else {
                        const canvas = await QRCode.toCanvas(text, options);
                        this.displayCanvasQR(canvas, text);
                    }
                    
                    this.addToHistory(text);
                    
                } catch (error) {
                    this.showStatus('生成二维码失败：' + error.message, 'error');
                }
            }
            
            displayCanvasQR(canvas, text) {
                this.currentQRData = { canvas, text, type: 'canvas' };
                
                const container = document.createElement('div');
                container.className = 'qr-code-container';
                
                canvas.className = 'qr-code-canvas';
                container.appendChild(canvas);
                
                const overlay = document.createElement('div');
                overlay.className = 'download-overlay';
                
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-btn';
                downloadBtn.innerHTML = '📥 下载';
                downloadBtn.onclick = () => this.downloadQR();
                
                overlay.appendChild(downloadBtn);
                container.appendChild(overlay);
                
                this.qrPreview.innerHTML = '';
                this.qrPreview.appendChild(container);
            }
            
            displaySVGQR(svgString, text) {
                this.currentQRData = { svgString, text, type: 'svg' };
                
                const container = document.createElement('div');
                container.className = 'qr-code-container';
                container.innerHTML = svgString;
                
                const svg = container.querySelector('svg');
                if (svg) {
                    svg.style.borderRadius = '8px';
                    svg.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                }
                
                const overlay = document.createElement('div');
                overlay.className = 'download-overlay';
                
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-btn';
                downloadBtn.innerHTML = '📥 下载';
                downloadBtn.onclick = () => this.downloadQR();
                
                overlay.appendChild(downloadBtn);
                container.appendChild(overlay);
                
                this.qrPreview.innerHTML = '';
                this.qrPreview.appendChild(container);
            }
            
            showPlaceholder() {
                this.qrPreview.innerHTML = `
                    <div class="qr-placeholder">
                        <svg width="80" height="80" viewBox="0 0 80 80" fill="none">
                            <rect x="10" y="10" width="20" height="20" stroke="#d1d5db" stroke-width="2" fill="none"/>
                            <rect x="50" y="10" width="20" height="20" stroke="#d1d5db" stroke-width="2" fill="none"/>
                            <rect x="10" y="50" width="20" height="20" stroke="#d1d5db" stroke-width="2" fill="none"/>
                            <rect x="35" y="35" width="10" height="10" stroke="#d1d5db" stroke-width="2" fill="none"/>
                        </svg>
                        <span>输入内容后自动生成二维码</span>
                    </div>
                `;
                this.currentQRData = null;
            }
            
            downloadQR() {
                if (!this.currentQRData) {
                    this.showStatus('请先生成二维码', 'error');
                    return;
                }
                
                const timestamp = new Date().toISOString().slice(0, 19).replace(/[T:]/g, '-');
                const filename = `qrcode-${timestamp}`;
                
                if (this.currentQRData.type === 'canvas') {
                    const format = this.outputFormat.value;
                    const mimeType = format === 'jpeg' ? 'image/jpeg' : 'image/png';
                    
                    this.currentQRData.canvas.toBlob(blob => {
                        this.downloadBlob(blob, `${filename}.${format}`);
                    }, mimeType, 0.92);
                } else if (this.currentQRData.type === 'svg') {
                    const blob = new Blob([this.currentQRData.svgString], { type: 'image/svg+xml' });
                    this.downloadBlob(blob, `${filename}.svg`);
                }
                
                this.showStatus('二维码已下载', 'success');
            }
            
            async copyQR() {
                if (!this.currentQRData) {
                    this.showStatus('请先生成二维码', 'error');
                    return;
                }
                
                try {
                    if (this.currentQRData.type === 'canvas') {
                        this.currentQRData.canvas.toBlob(async blob => {
                            await navigator.clipboard.write([
                                new ClipboardItem({ 'image/png': blob })
                            ]);
                            this.showStatus('二维码已复制到剪贴板', 'success');
                        });
                    } else {
                        await navigator.clipboard.writeText(this.currentQRData.svgString);
                        this.showStatus('SVG代码已复制到剪贴板', 'success');
                    }
                } catch (error) {
                    this.showStatus('复制失败，请手动下载', 'error');
                }
            }
            
            async generateBatch() {
                const texts = this.batchText.value.trim().split('\n').filter(text => text.trim());
                if (texts.length === 0) {
                    this.showStatus('请输入批量内容', 'error');
                    return;
                }
                
                this.batchResult.innerHTML = '';
                this.batchQRs = [];
                
                const options = {
                    errorCorrectionLevel: this.errorLevel.value,
                    type: 'image/png',
                    quality: 0.92,
                    margin: 1,
                    color: {
                        dark: this.foregroundColor.value,
                        light: this.backgroundColor.value
                    },
                    width: 150
                };
                
                for (let i = 0; i < texts.length; i++) {
                    const text = texts[i].trim();
                    try {
                        const canvas = await QRCode.toCanvas(text, options);
                        this.addBatchQR(canvas, text, i);
                    } catch (error) {
                        console.error(`生成第${i + 1}个二维码失败:`, error);
                    }
                }
                
                this.showStatus(`成功生成${this.batchQRs.length}个二维码`, 'success');
            }
            
            addBatchQR(canvas, text, index) {
                const item = document.createElement('div');
                item.className = 'batch-qr-item';
                
                const canvasClone = canvas.cloneNode(true);
                canvasClone.className = 'batch-qr-canvas';
                
                const textDiv = document.createElement('div');
                textDiv.className = 'batch-qr-text';
                textDiv.textContent = text.length > 30 ? text.substring(0, 30) + '...' : text;
                
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'batch-download-btn';
                downloadBtn.textContent = '下载';
                downloadBtn.onclick = () => {
                    canvas.toBlob(blob => {
                        this.downloadBlob(blob, `qrcode-batch-${index + 1}.png`);
                    });
                };
                
                item.appendChild(canvasClone);
                item.appendChild(textDiv);
                item.appendChild(downloadBtn);
                
                this.batchResult.appendChild(item);
                this.batchQRs.push({ canvas, text, index });
            }
            
            downloadAllBatch() {
                if (this.batchQRs.length === 0) {
                    this.showStatus('请先生成批量二维码', 'error');
                    return;
                }
                
                this.batchQRs.forEach(({ canvas, text, index }) => {
                    setTimeout(() => {
                        canvas.toBlob(blob => {
                            this.downloadBlob(blob, `qrcode-batch-${index + 1}.png`);
                        });
                    }, index * 100); // 延迟下载避免浏览器阻止
                });
                
                this.showStatus(`开始下载${this.batchQRs.length}个二维码`, 'success');
            }
            
            clearBatch() {
                this.batchText.value = '';
                this.batchResult.innerHTML = '';
                this.batchQRs = [];
                this.showStatus('已清空批量内容', 'success');
            }
            
            downloadBlob(blob, filename) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
            
            addToHistory(text) {
                const historyItem = {
                    text: text,
                    timestamp: new Date().toISOString(),
                    settings: {
                        size: this.qrSize.value,
                        errorLevel: this.errorLevel.value,
                        format: this.outputFormat.value,
                        foreground: this.foregroundColor.value,
                        background: this.backgroundColor.value
                    }
                };
                
                // 避免重复
                this.history = this.history.filter(item => item.text !== text);
                this.history.unshift(historyItem);
                
                // 限制历史记录数量
                if (this.history.length > 20) {
                    this.history = this.history.slice(0, 20);
                }
                
                localStorage.setItem('qrHistory', JSON.stringify(this.history));
                this.loadHistory();
            }
            
            loadHistory() {
                if (this.history.length === 0) {
                    this.qrHistory.innerHTML = '<div class="text-center text-gray-500 py-8">暂无生成历史</div>';
                    return;
                }
                
                this.qrHistory.innerHTML = this.history.map(item => {
                    const date = new Date(item.timestamp).toLocaleString();
                    const displayText = item.text.length > 50 ? item.text.substring(0, 50) + '...' : item.text;
                    
                    return `
                        <div class="history-item" data-text="${item.text.replace(/"/g, '&quot;')}">
                            <div class="history-text">${displayText}</div>
                            <div class="history-time">${date}</div>
                        </div>
                    `;
                }).join('');
                
                // 绑定历史记录点击事件
                this.qrHistory.querySelectorAll('.history-item').forEach(item => {
                    item.addEventListener('click', () => {
                        const text = item.dataset.text;
                        this.qrText.value = text;
                        this.generateQR();
                        this.showStatus('已加载历史记录', 'success');
                    });
                });
            }
            
            clearHistory() {
                this.history = [];
                localStorage.removeItem('qrHistory');
                this.loadHistory();
                this.showStatus('已清空生成历史', 'success');
            }
            
            showStatus(message, type) {
                const className = type === 'error' ? 'error-text' : 'success-text';
                this.statusInfo.innerHTML = `<div class="${className}">${message}</div>`;
                
                // 3秒后自动清除状态
                setTimeout(() => {
                    this.statusInfo.innerHTML = '';
                }, 3000);
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new QRGenerator();
        });
    </script>
</body>
</html>