import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import ToolContainer from '@/views/ToolContainer.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/tool/:toolId',
    name: 'Tool',
    component: ToolContainer,
    props: true
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router