<template>
  <div class="search-bar">
    <input 
      type="text" 
      :value="searchTerm"
      @input="handleInput"
      placeholder="搜索工具..."
    />
  </div>
</template>

<script setup>
const emit = defineEmits(['search'])

defineProps({
  searchTerm: {
    type: String,
    default: ''
  }
})

const handleInput = (event) => {
  emit('search', event.target.value)
}
</script>

<style scoped>
.search-bar {
  flex-grow: 1;
  margin: 0 2rem;
  max-width: 500px;
  position: relative;
}

/* 添加搜索图标装饰 */
.search-bar::before {
  content: '🔍';
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%) rotate(-10deg);
  font-size: 1.2rem;
  opacity: 0.6;
  z-index: 1;
  pointer-events: none;
}

.search-bar input {
  width: 100%;
  padding: 1rem 1.2rem 1rem 3rem;
  border: 3px dashed #B7DFBA;
  border-radius: 25px;
  background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(247,244,233,0.9) 100%);
  color: #4A4038;
  font-size: 1rem;
  font-family: 'Comic Sans MS', 'Chalkduster', '<PERSON>er Felt', cursive, sans-serif;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: rotate(-0.5deg);
  box-shadow: 2px 2px 4px rgba(123, 158, 137, 0.1);
}

.search-bar input:focus {
  outline: none;
  border-color: #7B9E89;
  background: linear-gradient(135deg, #FEFCF7 0%, rgba(183, 223, 186, 0.1) 100%);
  transform: rotate(0.5deg) scale(1.02);
  box-shadow: 
    3px 3px 6px rgba(123, 158, 137, 0.2),
    0 0 0 4px rgba(183, 223, 186, 0.3);
}

.search-bar input::placeholder {
  color: #A9998A;
  font-style: italic;
  opacity: 0.8;
}

/* 添加手绘装饰线条 */
.search-bar::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 20%;
  right: 20%;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(183, 223, 186, 0.6) 20%, 
    rgba(255, 182, 193, 0.6) 50%, 
    rgba(221, 160, 221, 0.6) 80%, 
    transparent 100%
  );
  border-radius: 50px;
  transform: rotate(-1deg);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.search-bar input:focus + ::after,
.search-bar:hover::after {
  opacity: 1;
}

/* 添加输入动画效果 */
.search-bar input:not(:placeholder-shown) {
  background: linear-gradient(135deg, #FEFCF7 0%, rgba(255, 182, 193, 0.1) 50%, rgba(255,255,255,0.9) 100%);
}
</style>
