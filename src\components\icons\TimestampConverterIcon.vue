<template>
  <svg 
    class="icon-svg" 
    viewBox="0 0 100 100" 
    xmlns="http://www.w3.org/2000/svg"
  >
    <!-- 背景圆圈 -->
    <circle 
      cx="50" 
      cy="50" 
      r="45" 
      fill="#fef3c7" 
      stroke="#f59e0b" 
      stroke-width="2"
      class="bg-circle"
    />
    
    <!-- 时钟外圈 -->
    <circle 
      cx="50" 
      cy="45" 
      r="25" 
      fill="#d97706" 
      stroke="#92400e" 
      stroke-width="2"
      class="clock-face"
    />
    
    <!-- 时钟内圈 -->
    <circle 
      cx="50" 
      cy="45" 
      r="20" 
      fill="#fef3c7" 
      class="clock-inner"
    />
    
    <!-- 时钟刻度 -->
    <g class="clock-marks">
      <line x1="50" y1="27" x2="50" y2="32" stroke="#92400e" stroke-width="2" class="mark mark-12" />
      <line x1="68" y1="45" x2="63" y2="45" stroke="#92400e" stroke-width="2" class="mark mark-3" />
      <line x1="50" y1="63" x2="50" y2="58" stroke="#92400e" stroke-width="2" class="mark mark-6" />
      <line x1="32" y1="45" x2="37" y2="45" stroke="#92400e" stroke-width="2" class="mark mark-9" />
    </g>
    
    <!-- 时钟指针 -->
    <g class="clock-hands">
      <!-- 时针 -->
      <line x1="50" y1="45" x2="50" y2="35" stroke="#92400e" stroke-width="3" stroke-linecap="round" class="hour-hand" />
      <!-- 分针 -->
      <line x1="50" y1="45" x2="58" y2="35" stroke="#92400e" stroke-width="2" stroke-linecap="round" class="minute-hand" />
      <!-- 中心点 -->
      <circle cx="50" cy="45" r="2" fill="#92400e" class="center-dot" />
    </g>
    
    <!-- 时间戳数字 -->
    <text x="50" y="80" text-anchor="middle" font-family="monospace" font-size="8" fill="#92400e" class="timestamp-text">1234567890</text>
    
    <!-- 转换箭头 -->
    <g class="conversion-arrows">
      <!-- 左箭头 -->
      <path 
        d="M20 45 L15 40 M20 45 L15 50 M20 45 L10 45" 
        stroke="#f59e0b" 
        stroke-width="2" 
        fill="none" 
        stroke-linecap="round"
        class="arrow-left"
      />
      
      <!-- 右箭头 -->
      <path 
        d="M80 45 L85 40 M80 45 L85 50 M80 45 L90 45" 
        stroke="#f59e0b" 
        stroke-width="2" 
        fill="none" 
        stroke-linecap="round"
        class="arrow-right"
      />
    </g>
    
    <!-- 日期显示 -->
    <text x="15" y="25" font-family="monospace" font-size="6" fill="#d97706" class="date-text">2024</text>
    <text x="75" y="25" font-family="monospace" font-size="6" fill="#d97706" class="date-text">12-31</text>
    
    <!-- 时间粒子效果 -->
    <g class="time-particles">
      <circle cx="25" cy="35" r="1" fill="#fbbf24" class="particle tp1" />
      <circle cx="75" cy="55" r="1" fill="#fbbf24" class="particle tp2" />
      <circle cx="30" cy="65" r="1" fill="#fbbf24" class="particle tp3" />
      <circle cx="70" cy="35" r="1" fill="#fbbf24" class="particle tp4" />
    </g>
  </svg>
</template>

<style scoped>
.icon-svg {
  width: 60px;
  height: 60px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.icon-svg:hover {
  transform: scale(1.1) rotate(-5deg);
}

.icon-svg:hover .bg-circle {
  fill: #fde68a;
  stroke: #d97706;
  animation: pulse 1s infinite;
}

.icon-svg:hover .clock-face {
  animation: clockGlow 1.5s ease-in-out infinite;
}

.icon-svg:hover .clock-inner {
  animation: innerPulse 1s ease-in-out infinite;
}

.icon-svg:hover .mark {
  animation: markBlink 2s ease-in-out infinite;
}

.icon-svg:hover .mark-12 {
  animation-delay: 0s;
}

.icon-svg:hover .mark-3 {
  animation-delay: 0.5s;
}

.icon-svg:hover .mark-6 {
  animation-delay: 1s;
}

.icon-svg:hover .mark-9 {
  animation-delay: 1.5s;
}

.icon-svg:hover .hour-hand {
  animation: hourRotate 4s linear infinite;
  transform-origin: 50px 45px;
}

.icon-svg:hover .minute-hand {
  animation: minuteRotate 2s linear infinite;
  transform-origin: 50px 45px;
}

.icon-svg:hover .center-dot {
  animation: centerPulse 1s ease-in-out infinite;
}

.icon-svg:hover .timestamp-text {
  animation: numberChange 3s ease-in-out infinite;
}

.icon-svg:hover .arrow-left {
  animation: arrowSlideLeft 1.5s ease-in-out infinite;
}

.icon-svg:hover .arrow-right {
  animation: arrowSlideRight 1.5s ease-in-out infinite;
}

.icon-svg:hover .date-text {
  animation: dateFlicker 2s ease-in-out infinite;
}

.icon-svg:hover .particle {
  animation: particleDrift 3s ease-in-out infinite;
}

.icon-svg:hover .tp1 {
  animation-delay: 0s;
}

.icon-svg:hover .tp2 {
  animation-delay: 0.75s;
}

.icon-svg:hover .tp3 {
  animation-delay: 1.5s;
}

.icon-svg:hover .tp4 {
  animation-delay: 2.25s;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes clockGlow {
  0%, 100% { filter: drop-shadow(0 0 5px #f59e0b); }
  50% { filter: drop-shadow(0 0 15px #fbbf24); }
}

@keyframes innerPulse {
  0%, 100% { fill: #fef3c7; }
  50% { fill: #fde68a; }
}

@keyframes markBlink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

@keyframes hourRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes minuteRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes centerPulse {
  0%, 100% { r: 2; }
  50% { r: 3; }
}

@keyframes numberChange {
  0%, 100% { opacity: 1; }
  25% { opacity: 0.5; }
  50% { opacity: 1; }
  75% { opacity: 0.7; }
}

@keyframes arrowSlideLeft {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(-5px); }
}

@keyframes arrowSlideRight {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(5px); }
}

@keyframes dateFlicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes particleDrift {
  0%, 100% { transform: translateY(0) scale(1); }
  33% { transform: translateY(-8px) scale(1.3); }
  66% { transform: translateY(4px) scale(0.7); }
}
</style>