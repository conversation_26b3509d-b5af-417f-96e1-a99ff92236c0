<template>
  <div class="tool-card" :data-category="tool.category">
    <div class="card-icon" @click="openTool">
      <component :is="getIconComponent(tool.id)" class="tool-svg-icon" />
    </div>
    <h3 class="card-title">{{ tool.name }}</h3>
    <p class="card-description">{{ tool.description }}</p>
    <div class="card-tags">
      <span v-for="tag in tool.tags" :key="tag">{{ tag }}</span>
    </div>
    <button class="card-action" @click="openTool">使用工具</button>
  </div>
</template>

<script setup>
import JsonFormatterIcon from './icons/JsonFormatterIcon.vue'
import Base64EncoderIcon from './icons/Base64EncoderIcon.vue'
import Md5HashIcon from './icons/Md5HashIcon.vue'
import UrlEncoderIcon from './icons/UrlEncoderIcon.vue'
import UuidGeneratorIcon from './icons/UuidGeneratorIcon.vue'
import TimestampConverterIcon from './icons/TimestampConverterIcon.vue'
import BaseConverterIcon from './icons/BaseConverterIcon.vue'
import CaseConverterIcon from './icons/CaseConverterIcon.vue'
import WordCounterIcon from './icons/WordCounterIcon.vue'
import QrGeneratorIcon from './icons/QrGeneratorIcon.vue'

const props = defineProps({
  tool: {
    type: Object,
    required: true
  }
})

const iconComponents = {
  'json-formatter': JsonFormatterIcon,
  'base64-encoder': Base64EncoderIcon,
  'md5-hash': Md5HashIcon,
  'url-encoder': UrlEncoderIcon,
  'uuid-generator': UuidGeneratorIcon,
  'timestamp-converter': TimestampConverterIcon,
  'base-converter': BaseConverterIcon,
  'case-converter': CaseConverterIcon,
  'word-counter': WordCounterIcon,
  'qr-generator': QrGeneratorIcon
}

const getIconComponent = (toolId) => {
  return iconComponents[toolId] || 'div'
}

import { useRouter } from 'vue-router'

const router = useRouter()

const openTool = () => {
  router.push(`/tool/${props.tool.id}`)
}
</script>

<style scoped>
.tool-card {
  background: linear-gradient(135deg, #FEFCF7 0%, rgba(255,255,255,0.95) 100%);
  border-radius: 20px;
  padding: 1.8rem;
  box-shadow: 
    3px 3px 0px rgba(183, 223, 186, 0.3),
    0 6px 20px rgba(123, 158, 137, 0.15);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  border: 3px dashed #B7DFBA;
  height: fit-content;
  position: relative;
  transform: rotate(-1deg);
  font-family: 'Comic Sans MS', 'Chalkduster', 'Marker Felt', cursive, sans-serif;
  overflow: hidden;
}

/* 添加纸张纹理 */
.tool-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(0,0,0,0.02) 1px, transparent 1px),
    radial-gradient(circle at 80% 80%, rgba(0,0,0,0.02) 1px, transparent 1px);
  background-size: 25px 25px, 35px 35px;
  pointer-events: none;
  z-index: 0;
}

.tool-card:nth-child(odd) {
  transform: rotate(1deg);
}

.tool-card:nth-child(even) {
  transform: rotate(-0.5deg);
}

.tool-card:hover {
  transform: rotate(2deg) translateY(-8px) scale(1.02);
  box-shadow: 
    5px 5px 0px rgba(183, 223, 186, 0.4),
    0 12px 30px rgba(123, 158, 137, 0.25);
  border-color: #7B9E89;
  background: linear-gradient(135deg, #FEFCF7 0%, rgba(255, 182, 193, 0.1) 50%, rgba(255,255,255,0.95) 100%);
}

/* 添加手绘高亮效果 */
.tool-card:hover::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 2px solid rgba(255, 182, 193, 0.6);
  border-radius: 25px;
  border-style: dashed;
  animation: highlight-pulse 2s ease-in-out infinite;
  z-index: -1;
}

@keyframes highlight-pulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.02); }
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(183, 223, 186, 0.3) 0%, rgba(255, 182, 193, 0.2) 100%);
  border-radius: 50%;
  margin-bottom: 1.2rem;
  color: #7B9E89;
  border: 3px dashed #B7DFBA;
  position: relative;
  z-index: 1;
  transform: rotate(-5deg);
  transition: all 0.3s ease;
}

.card-icon:hover {
  transform: rotate(5deg) scale(1.1);
  background: radial-gradient(circle, rgba(183, 223, 186, 0.5) 0%, rgba(255, 182, 193, 0.3) 100%);
}

.tool-svg-icon {
  width: 28px;
  height: 28px;
  filter: drop-shadow(1px 1px 2px rgba(123, 158, 137, 0.3));
}

.card-title {
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0 0 0.8rem 0;
  color: #4A4038;
  line-height: 1.3;
  position: relative;
  z-index: 1;
  text-shadow: 1px 1px 0px rgba(183, 223, 186, 0.3);
  transform: rotate(-0.5deg);
}

/* 添加手绘下划线 */
.card-title::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    #FFB6C1 20%, 
    #DDA0DD 50%, 
    #FFFFE0 80%, 
    transparent 100%
  );
  border-radius: 50px;
  transform: rotate(1deg);
}

.card-description {
  font-size: 0.95rem;
  color: #7D6E60;
  margin-bottom: 1.2rem;
  line-height: 1.6;
  position: relative;
  z-index: 1;
  font-style: italic;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
  margin-bottom: 1.8rem;
  position: relative;
  z-index: 1;
}

.card-tags span {
  display: inline-block;
  background: linear-gradient(45deg, rgba(183, 223, 186, 0.3), rgba(255, 182, 193, 0.3));
  color: #4A4038;
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 2px dashed rgba(123, 158, 137, 0.4);
  transform: rotate(-1deg);
  transition: all 0.3s ease;
}

.card-tags span:nth-child(even) {
  transform: rotate(1deg);
  background: linear-gradient(45deg, rgba(255, 182, 193, 0.3), rgba(221, 160, 221, 0.3));
}

.card-tags span:hover {
  transform: rotate(0deg) scale(1.05);
  background: linear-gradient(45deg, #B7DFBA, #FFB6C1);
  color: white;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.card-action {
  width: 100%;
  padding: 1rem 1.5rem;
  border: 3px dashed #7B9E89;
  background: linear-gradient(135deg, #B7DFBA 0%, #7B9E89 100%);
  color: white;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 700;
  font-size: 1rem;
  font-family: 'Comic Sans MS', 'Chalkduster', 'Marker Felt', cursive, sans-serif;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  z-index: 1;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
  transform: rotate(-1deg);
  box-shadow: 2px 2px 0px rgba(123, 158, 137, 0.3);
}

.card-action:hover {
  background: linear-gradient(135deg, #7B9E89 0%, #6B8E5A 100%);
  transform: rotate(1deg) scale(1.05);
  box-shadow: 4px 4px 0px rgba(123, 158, 137, 0.4);
}

.card-action:active {
  transform: rotate(0deg) scale(0.98);
  box-shadow: 1px 1px 0px rgba(123, 158, 137, 0.3);
}

/* 添加装饰性元素 */
.tool-card:nth-child(3n)::before {
  content: '✨';
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.2rem;
  opacity: 0.4;
  animation: sparkle 3s ease-in-out infinite;
  z-index: 1;
}

.tool-card:nth-child(5n)::before {
  content: '🎨';
  animation: bounce 4s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.4; }
  50% { transform: scale(1.3) rotate(180deg); opacity: 0.8; }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
}
</style>
