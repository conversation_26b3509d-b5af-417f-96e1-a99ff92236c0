<template>
  <div id="app">
    <router-view />
  </div>
</template>

<style>
#app {
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 100vh;
}

/* 确保body和html也没有默认边距 */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 100%;
}

/* 为主页组件设置合适的布局 */
.container {
  min-height: 100vh;
  width: 100%;
}

/* 确保工具容器能正确占据全屏 */
.tool-container {
  height: 100vh;
  overflow: hidden;
}
</style>
