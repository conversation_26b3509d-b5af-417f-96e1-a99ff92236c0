export const tools = [
    {
        id: 'json-formatter',
        name: 'JSON格式化',
        description: '格式化、压缩、验证JSON数据',
        category: 'development',
        icon: '📝',
        url: '/tools/development/json-formatter.html',
        tags: ['json', '格式化', '验证', '开发']
    },
    {
        id: 'base64-encoder',
        name: 'Base64编解码',
        description: 'Base64编码和解码工具',
        category: 'encoding',
        icon: '🔐',
        url: '/tools/encoding/base64-encoder.html',
        tags: ['base64', '编码', '解码', '加密']
    },
    {
        id: 'md5-hash',
        name: 'MD5哈希',
        description: 'MD5、SHA1、SHA256等哈希加密',
        category: 'security',
        icon: '🔒',
        url: '/tools/security/md5-hash.html',
        tags: ['md5', 'sha1', 'sha256', '哈希', '加密']
    },
    {
        id: 'url-encoder',
        name: 'URL编解码',
        description: 'URL编码解码和分析工具',
        category: 'encoding',
        icon: '🌐',
        url: '/tools/encoding/url-encoder.html',
        tags: ['url', '编码', '解码', '网络']
    },
    {
        id: 'uuid-generator',
        name: 'UUID生成器',
        description: '生成各种版本的UUID',
        category: 'development',
        icon: '🆔',
        url: '/tools/development/uuid-generator.html',
        tags: ['uuid', '生成器', '唯一标识', '开发']
    },
    {
        id: 'timestamp-converter',
        name: '时间戳转换',
        description: '时间戳与日期时间相互转换',
        category: 'conversion',
        icon: '⏰',
        url: '/tools/conversion/timestamp-converter.html',
        tags: ['时间戳', '日期', '转换', '时间']
    },
    {
        id: 'base-converter',
        name: '进制转换',
        description: '数字进制转换工具',
        category: 'conversion',
        icon: '🔢',
        url: '/tools/conversion/base-converter.html',
        tags: ['进制', '转换', '数字', '计算']
    },
    {
        id: 'case-converter',
        name: '大小写转换',
        description: '文本大小写格式转换',
        category: 'text',
        icon: '🔤',
        url: '/tools/text/case-converter.html',
        tags: ['大小写', '文本', '转换', '格式']
    },
    {
        id: 'word-counter',
        name: '字数统计',
        description: '文本字数、词数、行数统计分析',
        category: 'text',
        icon: '📊',
        url: '/tools/text/word-counter.html',
        tags: ['字数', '统计', '文本', '分析']
    },
    {
        id: 'qr-generator',
        name: '二维码生成',
        description: '生成各种类型的二维码',
        category: 'image',
        icon: '📱',
        url: '/tools/image/qr-generator.html',
        tags: ['二维码', '生成', '扫码', '图片']
    }
];

export const categories = [
    { id: 'all', name: '全部工具' },
    { id: 'development', name: '开发工具' },
    { id: 'encoding', name: '编码/加密' },
    { id: 'security', name: '安全工具' },
    { id: 'conversion', name: '转换工具' },
    { id: 'text', name: '文本工具' },
    { id: 'image', name: '图片工具' },
    { id: 'pdf', name: 'PDF工具' },
    { id: 'audio', name: '音频工具' },
    { id: 'video', name: '视频工具' },
    { id: 'network', name: '网络工具' },
    { id: 'office', name: '办公工具' },
    { id: 'life', name: '生活工具' }
];
