<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL编解码器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 手绘素描风格 */
        body {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            font-family: 'Comic Sans MS', cursive, sans-serif;
        }
        
        .sketch-border {
            border: 2px solid #10b981;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            position: relative;
        }
        
        .sketch-border::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 1px dashed #34d399;
            border-radius: 14px;
            pointer-events: none;
        }
        
        .btn-sketch {
            background: linear-gradient(145deg, #10b981, #059669);
            border: 2px solid #047857;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-sketch:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(16, 185, 129, 0.3);
        }
        
        .btn-sketch:active {
            transform: translateY(0);
        }
        
        .input-sketch {
            border: 2px solid #34d399;
            border-radius: 8px;
            padding: 12px;
            background: white;
            transition: border-color 0.3s ease;
        }
        
        .input-sketch:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        
        .result-area {
            background: linear-gradient(145deg, #f0fdf4, #ecfdf5);
            border: 2px solid #34d399;
            border-radius: 12px;
            min-height: 200px;
        }
        
        .error-text {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        .success-text {
            color: #059669;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        /* 装饰元素 */
        .doodle-star {
            position: absolute;
            color: #10b981;
            font-size: 20px;
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .url-icon {
            stroke: #10b981;
            stroke-width: 2;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
            animation: dash 3s linear infinite;
        }
        
        @keyframes dash {
            to { stroke-dashoffset: -20; }
        }
        
        .tab-active {
            background: linear-gradient(145deg, #10b981, #059669);
            color: white;
        }
        
        .tab-inactive {
            background: #f0fdf4;
            color: #047857;
            border: 2px solid #34d399;
        }
        
        .url-part {
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 2px;
            display: inline-block;
        }
        
        .url-protocol { background: #dbeafe; color: #1e40af; }
        .url-domain { background: #fef3c7; color: #92400e; }
        .url-path { background: #f3e8ff; color: #7c3aed; }
        .url-query { background: #ecfdf5; color: #047857; }
        .url-fragment { background: #fef2f2; color: #dc2626; }
    </style>
</head>
<body class="min-h-screen p-6">
    <div class="max-w-6xl mx-auto">
        <!-- 标题区域 -->
        <div class="text-center mb-8 relative">
            <div class="doodle-star" style="top: 10px; left: 15%;">🌐</div>
            <div class="doodle-star" style="top: 15px; right: 20%; animation-delay: 1s;">🔗</div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">URL编解码器</h1>
            <p class="text-lg text-gray-600">专业的URL编码解码和分析工具</p>
            
            <!-- URL图标 -->
            <svg class="absolute top-16 left-1/2 transform -translate-x-1/2" width="80" height="30" viewBox="0 0 80 30">
                <path class="url-icon" d="M10 15 Q25 5 40 15 Q55 25 70 15" stroke-dasharray="5,5" />
                <circle class="url-icon" cx="10" cy="15" r="3" style="animation-delay: 0.5s;" />
                <circle class="url-icon" cx="70" cy="15" r="3" style="animation-delay: 1s;" />
            </svg>
        </div>
        
        <!-- 选项卡 -->
        <div class="flex justify-center mb-6">
            <div class="flex bg-white rounded-lg p-1 sketch-border">
                <button id="encodeTab" class="tab-active px-6 py-3 rounded-md font-semibold transition-all">
                    🔐 编码
                </button>
                <button id="decodeTab" class="tab-inactive px-6 py-3 rounded-md font-semibold transition-all">
                    🔓 解码
                </button>
                <button id="analyzeTab" class="tab-inactive px-6 py-3 rounded-md font-semibold transition-all">
                    🔍 分析
                </button>
            </div>
        </div>
        
        <!-- 编码模式 -->
        <div id="encodeMode" class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 输入区域 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">📝</span>
                    原始URL
                </h2>
                
                <textarea 
                    id="encodeInput" 
                    class="input-sketch w-full h-64 resize-none font-mono text-sm"
                    placeholder="请输入要编码的URL...\n\n例如：\nhttps://example.com/search?q=你好世界&type=文档\nhttps://example.com/路径/文件名.html"
                ></textarea>
                
                <!-- 编码选项 -->
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">编码类型：</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="flex items-center">
                            <input type="radio" name="encodeType" value="component" checked class="mr-2">
                            <span class="text-sm">URL组件编码</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="encodeType" value="full" class="mr-2">
                            <span class="text-sm">完整URL编码</span>
                        </label>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex flex-wrap gap-3 mt-4">
                    <button id="encodeBtn" class="btn-sketch">
                        <span class="mr-1">🔐</span>
                        编码
                    </button>
                    <button id="clearEncodeBtn" class="btn-sketch bg-gradient-to-r from-gray-500 to-gray-600 border-gray-700">
                        <span class="mr-1">🗑️</span>
                        清空
                    </button>
                </div>
            </div>
            
            <!-- 输出区域 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">📋</span>
                    编码结果
                </h2>
                
                <div class="result-area p-4 relative">
                    <pre id="encodeOutput" class="font-mono text-sm whitespace-pre-wrap break-words h-64 overflow-auto"></pre>
                    
                    <!-- 复制按钮 -->
                    <button id="copyEncodeBtn" class="absolute top-2 right-2 bg-emerald-500 hover:bg-emerald-600 text-white px-3 py-1 rounded text-xs transition-colors">
                        📋 复制
                    </button>
                </div>
                
                <!-- 状态信息 -->
                <div id="encodeStatus" class="mt-4 text-sm"></div>
            </div>
        </div>
        
        <!-- 解码模式 -->
        <div id="decodeMode" class="grid grid-cols-1 lg:grid-cols-2 gap-8 hidden">
            <!-- 输入区域 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">🔐</span>
                    编码的URL
                </h2>
                
                <textarea 
                    id="decodeInput" 
                    class="input-sketch w-full h-64 resize-none font-mono text-sm"
                    placeholder="请输入要解码的URL...\n\n例如：\nhttps://example.com/search?q=%E4%BD%A0%E5%A5%BD%E4%B8%96%E7%95%8C"
                ></textarea>
                
                <!-- 操作按钮 -->
                <div class="flex flex-wrap gap-3 mt-4">
                    <button id="decodeBtn" class="btn-sketch">
                        <span class="mr-1">🔓</span>
                        解码
                    </button>
                    <button id="clearDecodeBtn" class="btn-sketch bg-gradient-to-r from-gray-500 to-gray-600 border-gray-700">
                        <span class="mr-1">🗑️</span>
                        清空
                    </button>
                </div>
            </div>
            
            <!-- 输出区域 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">📋</span>
                    解码结果
                </h2>
                
                <div class="result-area p-4 relative">
                    <pre id="decodeOutput" class="font-mono text-sm whitespace-pre-wrap break-words h-64 overflow-auto"></pre>
                    
                    <!-- 复制按钮 -->
                    <button id="copyDecodeBtn" class="absolute top-2 right-2 bg-emerald-500 hover:bg-emerald-600 text-white px-3 py-1 rounded text-xs transition-colors">
                        📋 复制
                    </button>
                </div>
                
                <!-- 状态信息 -->
                <div id="decodeStatus" class="mt-4 text-sm"></div>
            </div>
        </div>
        
        <!-- 分析模式 -->
        <div id="analyzeMode" class="hidden">
            <div class="sketch-border bg-white p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">🔍</span>
                    URL分析
                </h2>
                
                <textarea 
                    id="analyzeInput" 
                    class="input-sketch w-full h-32 resize-none font-mono text-sm mb-4"
                    placeholder="请输入要分析的URL...\n\n例如：https://example.com:8080/path/to/page?param1=value1&param2=value2#section"
                ></textarea>
                
                <button id="analyzeBtn" class="btn-sketch">
                    <span class="mr-1">🔍</span>
                    分析URL
                </button>
            </div>
            
            <!-- 分析结果 -->
            <div id="analyzeResult" class="sketch-border bg-white p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">URL组件分析</h3>
                <div id="urlComponents" class="space-y-3"></div>
            </div>
        </div>
        
        <!-- 功能说明 -->
        <div class="mt-8 sketch-border bg-white p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                <span class="mr-2">💡</span>
                URL编解码说明
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">🔐 URL编码</h4>
                    <p>将特殊字符转换为%编码格式，确保URL在网络传输中的正确性</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">🔓 URL解码</h4>
                    <p>将%编码格式还原为原始字符，便于阅读和理解</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">🔍 URL分析</h4>
                    <p>解析URL的各个组成部分，包括协议、域名、路径、参数等</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        class URLConverter {
            constructor() {
                this.initElements();
                this.bindEvents();
                this.loadExample();
            }
            
            initElements() {
                // 选项卡
                this.encodeTab = document.getElementById('encodeTab');
                this.decodeTab = document.getElementById('decodeTab');
                this.analyzeTab = document.getElementById('analyzeTab');
                this.encodeMode = document.getElementById('encodeMode');
                this.decodeMode = document.getElementById('decodeMode');
                this.analyzeMode = document.getElementById('analyzeMode');
                
                // 编码相关
                this.encodeInput = document.getElementById('encodeInput');
                this.encodeOutput = document.getElementById('encodeOutput');
                this.encodeStatus = document.getElementById('encodeStatus');
                this.encodeBtn = document.getElementById('encodeBtn');
                this.clearEncodeBtn = document.getElementById('clearEncodeBtn');
                this.copyEncodeBtn = document.getElementById('copyEncodeBtn');
                
                // 解码相关
                this.decodeInput = document.getElementById('decodeInput');
                this.decodeOutput = document.getElementById('decodeOutput');
                this.decodeStatus = document.getElementById('decodeStatus');
                this.decodeBtn = document.getElementById('decodeBtn');
                this.clearDecodeBtn = document.getElementById('clearDecodeBtn');
                this.copyDecodeBtn = document.getElementById('copyDecodeBtn');
                
                // 分析相关
                this.analyzeInput = document.getElementById('analyzeInput');
                this.analyzeBtn = document.getElementById('analyzeBtn');
                this.analyzeResult = document.getElementById('analyzeResult');
                this.urlComponents = document.getElementById('urlComponents');
            }
            
            bindEvents() {
                // 选项卡切换
                this.encodeTab.addEventListener('click', () => this.switchTab('encode'));
                this.decodeTab.addEventListener('click', () => this.switchTab('decode'));
                this.analyzeTab.addEventListener('click', () => this.switchTab('analyze'));
                
                // 编码功能
                this.encodeBtn.addEventListener('click', () => this.encodeURL());
                this.clearEncodeBtn.addEventListener('click', () => this.clearEncode());
                this.copyEncodeBtn.addEventListener('click', () => this.copyResult('encode'));
                
                // 解码功能
                this.decodeBtn.addEventListener('click', () => this.decodeURL());
                this.clearDecodeBtn.addEventListener('click', () => this.clearDecode());
                this.copyDecodeBtn.addEventListener('click', () => this.copyResult('decode'));
                
                // 分析功能
                this.analyzeBtn.addEventListener('click', () => this.analyzeURL());
                
                // 实时编解码
                this.encodeInput.addEventListener('input', () => {
                    this.debounce(() => this.encodeURL(true), 300)();
                });
                
                this.decodeInput.addEventListener('input', () => {
                    this.debounce(() => this.decodeURL(true), 300)();
                });
            }
            
            loadExample() {
                this.encodeInput.value = 'https://example.com/搜索?查询=你好世界&类型=文档#章节';
                this.encodeURL();
            }
            
            switchTab(mode) {
                // 重置所有选项卡
                [this.encodeTab, this.decodeTab, this.analyzeTab].forEach(tab => {
                    tab.className = 'tab-inactive px-6 py-3 rounded-md font-semibold transition-all';
                });
                
                [this.encodeMode, this.decodeMode, this.analyzeMode].forEach(mode => {
                    mode.classList.add('hidden');
                });
                
                // 激活选中的选项卡
                if (mode === 'encode') {
                    this.encodeTab.className = 'tab-active px-6 py-3 rounded-md font-semibold transition-all';
                    this.encodeMode.classList.remove('hidden');
                } else if (mode === 'decode') {
                    this.decodeTab.className = 'tab-active px-6 py-3 rounded-md font-semibold transition-all';
                    this.decodeMode.classList.remove('hidden');
                } else if (mode === 'analyze') {
                    this.analyzeTab.className = 'tab-active px-6 py-3 rounded-md font-semibold transition-all';
                    this.analyzeMode.classList.remove('hidden');
                }
            }
            
            encodeURL(silent = false) {
                try {
                    const input = this.encodeInput.value.trim();
                    if (!input) {
                        if (!silent) this.showStatus('请输入要编码的URL', 'error', 'encode');
                        this.encodeOutput.textContent = '';
                        return;
                    }
                    
                    const encodeType = document.querySelector('input[name="encodeType"]:checked').value;
                    let encoded;
                    
                    if (encodeType === 'component') {
                        // 只编码URL组件（查询参数、路径等）
                        encoded = input.replace(/[^\w\-\.~:/?#\[\]@!$&'()*+,;=]/g, function(char) {
                            return encodeURIComponent(char);
                        });
                    } else {
                        // 完整URL编码
                        encoded = encodeURIComponent(input);
                    }
                    
                    this.encodeOutput.textContent = encoded;
                    
                    if (!silent) {
                        const stats = `编码成功！原URL: ${input.length} 字符，编码后: ${encoded.length} 字符`;
                        this.showStatus(stats, 'success', 'encode');
                    }
                    
                } catch (error) {
                    this.showStatus(`编码失败: ${error.message}`, 'error', 'encode');
                    this.encodeOutput.textContent = '';
                }
            }
            
            decodeURL(silent = false) {
                try {
                    const input = this.decodeInput.value.trim();
                    if (!input) {
                        if (!silent) this.showStatus('请输入要解码的URL', 'error', 'decode');
                        this.decodeOutput.textContent = '';
                        return;
                    }
                    
                    const decoded = decodeURIComponent(input);
                    this.decodeOutput.textContent = decoded;
                    
                    if (!silent) {
                        const stats = `解码成功！编码URL: ${input.length} 字符，解码后: ${decoded.length} 字符`;
                        this.showStatus(stats, 'success', 'decode');
                    }
                    
                } catch (error) {
                    this.showStatus(`解码失败: ${error.message}`, 'error', 'decode');
                    this.decodeOutput.textContent = '';
                }
            }
            
            analyzeURL() {
                try {
                    const input = this.analyzeInput.value.trim();
                    if (!input) {
                        this.showStatus('请输入要分析的URL', 'error', 'analyze');
                        return;
                    }
                    
                    const url = new URL(input);
                    const components = {
                        '完整URL': input,
                        '协议': url.protocol,
                        '主机名': url.hostname,
                        '端口': url.port || '默认端口',
                        '路径': url.pathname,
                        '查询字符串': url.search,
                        '片段标识符': url.hash,
                        '源': url.origin
                    };
                    
                    let html = '';
                    for (const [key, value] of Object.entries(components)) {
                        if (value) {
                            const colorClass = this.getComponentColorClass(key);
                            html += `
                                <div class="flex items-start gap-3">
                                    <span class="font-medium text-gray-700 w-24 flex-shrink-0">${key}:</span>
                                    <span class="url-part ${colorClass} flex-1">${value}</span>
                                </div>
                            `;
                        }
                    }
                    
                    // 解析查询参数
                    if (url.search) {
                        html += '<div class="mt-4 pt-4 border-t border-gray-200">';
                        html += '<h4 class="font-medium text-gray-800 mb-2">查询参数详情:</h4>';
                        
                        const params = new URLSearchParams(url.search);
                        for (const [key, value] of params) {
                            html += `
                                <div class="flex items-start gap-3 ml-4">
                                    <span class="font-mono text-sm text-blue-600">${key}:</span>
                                    <span class="font-mono text-sm text-green-600">${value}</span>
                                </div>
                            `;
                        }
                        html += '</div>';
                    }
                    
                    this.urlComponents.innerHTML = html;
                    this.showStatus('URL分析完成！', 'success', 'analyze');
                    
                } catch (error) {
                    this.showStatus(`URL分析失败: ${error.message}`, 'error', 'analyze');
                    this.urlComponents.innerHTML = '<div class="text-red-500">无效的URL格式</div>';
                }
            }
            
            getComponentColorClass(component) {
                const colorMap = {
                    '协议': 'url-protocol',
                    '主机名': 'url-domain',
                    '路径': 'url-path',
                    '查询字符串': 'url-query',
                    '片段标识符': 'url-fragment'
                };
                return colorMap[component] || 'bg-gray-100 text-gray-700';
            }
            
            clearEncode() {
                this.encodeInput.value = '';
                this.encodeOutput.textContent = '';
                this.encodeStatus.innerHTML = '';
                this.showStatus('已清空编码内容', 'success', 'encode');
            }
            
            clearDecode() {
                this.decodeInput.value = '';
                this.decodeOutput.textContent = '';
                this.decodeStatus.innerHTML = '';
                this.showStatus('已清空解码内容', 'success', 'decode');
            }
            
            async copyResult(mode) {
                const output = mode === 'encode' ? this.encodeOutput : this.decodeOutput;
                const result = output.textContent;
                
                if (!result) {
                    this.showStatus('没有可复制的内容', 'error', mode);
                    return;
                }
                
                try {
                    await navigator.clipboard.writeText(result);
                    this.showStatus('已复制到剪贴板！', 'success', mode);
                    
                    // 按钮反馈
                    const copyBtn = mode === 'encode' ? this.copyEncodeBtn : this.copyDecodeBtn;
                    const originalText = copyBtn.innerHTML;
                    copyBtn.innerHTML = '✅ 已复制';
                    setTimeout(() => {
                        copyBtn.innerHTML = originalText;
                    }, 2000);
                    
                } catch (error) {
                    this.showStatus('复制失败，请手动复制', 'error', mode);
                }
            }
            
            showStatus(message, type, mode) {
                const className = type === 'error' ? 'error-text' : 'success-text';
                let statusElement;
                
                if (mode === 'encode') {
                    statusElement = this.encodeStatus;
                } else if (mode === 'decode') {
                    statusElement = this.decodeStatus;
                } else {
                    // 对于分析模式，在分析结果上方显示状态
                    statusElement = this.analyzeResult.querySelector('.status') || 
                        (() => {
                            const div = document.createElement('div');
                            div.className = 'status mb-4';
                            this.analyzeResult.insertBefore(div, this.analyzeResult.firstChild);
                            return div;
                        })();
                }
                
                statusElement.innerHTML = `<div class="${className}">${message}</div>`;
                
                // 3秒后自动清除状态
                setTimeout(() => {
                    statusElement.innerHTML = '';
                }, 3000);
            }
            
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new URLConverter();
        });
    </script>
</body>
</html>