.sketch-container {
    background-color: #fdfaf4; /* Soft textured paper background */
    font-family: 'Comic Sans MS', 'Chalkduster', 'cursive';
    border: 3px solid #444;
    border-radius: 15px;
    box-shadow: 5px 5px 0px #d4d4d4;
    padding: 20px;
}

.sketch-header {
    text-align: center;
    border-bottom: 2px dashed #888;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.sketch-header h1 {
    font-size: 2.5em;
    color: #333;
    text-shadow: 2px 2px #eee;
}

.sketch-main-content {
    padding: 10px;
}

.sketch-tool-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 25px;
}

.sketch-tool-card {
    background-color: #fff;
    border: 2px solid #555;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 3px 3px 0px #e0e0e0;
    transition: all 0.2s ease-in-out;
}

.sketch-tool-card:hover {
    transform: rotate(2deg);
    box-shadow: 5px 5px 0px #ccc;
}

.sketch-sidebar {
    border-right: 2px dashed #888;
    padding: 15px;
}

.sketch-sidebar ul {
    list-style-type: none; /* Hand-drawn bullets could be added with ::before */
    padding: 0;
}

.sketch-sidebar li a {
    text-decoration: none;
    color: #555;
    font-size: 1.2em;
    display: block;
    padding: 10px;
    border-radius: 8px;
}

.sketch-sidebar li a:hover {
    background-color: #f0f0f0;
    color: #000;
}

/* 手绘风格搜索栏 */
.sketch-header .search-bar {
    flex-grow: 1;
    margin: 0 20px;
}

.sketch-header .search-bar input {
    width: 100%;
    max-width: 500px;
    padding: 12px 16px;
    border: 3px solid #666;
    border-radius: 20px;
    background-color: #fff;
    color: #333;
    font-family: 'Comic Sans MS', 'Chalkduster', cursive;
    font-size: 16px;
    box-shadow: 2px 2px 0px #ddd;
    transition: all 0.2s ease;
}

.sketch-header .search-bar input:focus {
    outline: none;
    border-color: #444;
    box-shadow: 3px 3px 0px #ccc;
    transform: translateY(-1px);
}

.sketch-header .search-bar input::placeholder {
    color: #888;
    font-style: italic;
}

/* 手绘风格主题切换器 */
.sketch-header .theme-switcher select {
    padding: 10px 15px;
    border: 3px solid #666;
    border-radius: 15px;
    background-color: #fff;
    color: #333;
    font-family: 'Comic Sans MS', 'Chalkduster', cursive;
    font-size: 14px;
    cursor: pointer;
    box-shadow: 2px 2px 0px #ddd;
    transition: all 0.2s ease;
}

.sketch-header .theme-switcher select:hover {
    box-shadow: 3px 3px 0px #ccc;
    transform: translateY(-1px);
}

.sketch-header .theme-switcher select:focus {
    outline: none;
    border-color: #444;
}

/* 手绘风格标题 */
.sketch-header .logo h1 {
    font-family: 'Comic Sans MS', 'Chalkduster', cursive;
    color: #333;
    text-shadow: 2px 2px 0px #eee;
    margin: 0;
    font-size: 2em;
    transform: rotate(-2deg);
}