import { ref, computed, onMounted, onUnmounted } from 'vue'

export function useTimestamp() {
  const currentTime = ref(new Date())
  const timer = ref(null)

  // 启动定时器
  const startTimer = () => {
    timer.value = setInterval(() => {
      currentTime.value = new Date()
    }, 1000)
  }

  // 停止定时器
  const stopTimer = () => {
    if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
  }

  // 当前时间戳（秒）
  const currentTimestamp = computed(() => Math.floor(currentTime.value.getTime() / 1000))

  // 当前时间戳（毫秒）
  const currentTimestampMs = computed(() => currentTime.value.getTime())

  // 格式化当前时间
  const currentDateTime = computed(() => formatDateTime(currentTime.value))

  // 时间戳转日期
  const timestampToDate = (timestamp) => {
    try {
      let ts = parseInt(timestamp)
      
      // 判断是秒还是毫秒
      if (ts.toString().length === 10) {
        ts *= 1000 // 转换为毫秒
      } else if (ts.toString().length !== 13) {
        throw new Error('时间戳格式不正确，应为10位（秒）或13位（毫秒）')
      }
      
      const date = new Date(ts)
      
      if (isNaN(date.getTime())) {
        throw new Error('无效的时间戳')
      }
      
      return {
        success: true,
        date,
        timestamp: Math.floor(ts / 1000),
        formats: getDateFormats(date)
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 日期转时间戳
  const dateToTimestamp = (dateValue, timeValue = '00:00:00', timezone = 'local') => {
    try {
      if (!dateValue) {
        throw new Error('请选择日期')
      }
      
      const dateTimeString = `${dateValue}T${timeValue}`
      let date
      
      if (timezone === 'local') {
        date = new Date(dateTimeString)
      } else if (timezone === 'UTC') {
        date = new Date(dateTimeString + 'Z')
      } else {
        // 对于其他时区，使用本地时间处理
        date = new Date(dateTimeString)
      }
      
      if (isNaN(date.getTime())) {
        throw new Error('无效的日期时间')
      }
      
      const timestamp = Math.floor(date.getTime() / 1000)
      
      return {
        success: true,
        date,
        timestamp,
        formats: getTimestampFormats(date, timestamp)
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 获取日期的多种格式
  const getDateFormats = (date) => {
    return [
      { label: '标准格式', value: formatDateTime(date), type: 'text' },
      { label: 'ISO 8601', value: date.toISOString(), type: 'text' },
      { label: 'UTC格式', value: date.toUTCString(), type: 'text' },
      { label: '本地格式', value: date.toLocaleString('zh-CN'), type: 'text' },
      { label: '日期部分', value: date.toLocaleDateString('zh-CN'), type: 'text' },
      { label: '时间部分', value: date.toLocaleTimeString('zh-CN'), type: 'text' },
      { label: '相对时间', value: getRelativeTime(date), type: 'text' }
    ]
  }

  // 获取时间戳的多种格式
  const getTimestampFormats = (date, timestamp) => {
    return [
      { label: '秒级时间戳', value: timestamp.toString(), type: 'timestamp' },
      { label: '毫秒级时间戳', value: (timestamp * 1000).toString(), type: 'timestamp' },
      { label: 'JavaScript时间戳', value: date.getTime().toString(), type: 'timestamp' },
      { label: 'Unix时间戳', value: timestamp.toString(), type: 'timestamp' }
    ]
  }

  // 格式化日期时间
  const formatDateTime = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  // 获取相对时间
  const getRelativeTime = (date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffSec = Math.floor(diffMs / 1000)
    const diffMin = Math.floor(diffSec / 60)
    const diffHour = Math.floor(diffMin / 60)
    const diffDay = Math.floor(diffHour / 24)
    
    if (Math.abs(diffSec) < 60) {
      return diffSec >= 0 ? `${diffSec}秒前` : `${Math.abs(diffSec)}秒后`
    } else if (Math.abs(diffMin) < 60) {
      return diffMin >= 0 ? `${diffMin}分钟前` : `${Math.abs(diffMin)}分钟后`
    } else if (Math.abs(diffHour) < 24) {
      return diffHour >= 0 ? `${diffHour}小时前` : `${Math.abs(diffHour)}小时后`
    } else {
      return diffDay >= 0 ? `${diffDay}天前` : `${Math.abs(diffDay)}天后`
    }
  }

  // 获取常用时间格式
  const getCommonFormats = () => {
    const now = currentTime.value
    const timestamp = Math.floor(now.getTime() / 1000)
    
    return [
      { label: 'Unix时间戳 (秒)', value: timestamp.toString() },
      { label: 'JavaScript时间戳 (毫秒)', value: now.getTime().toString() },
      { label: 'ISO 8601', value: now.toISOString() },
      { label: 'RFC 2822', value: now.toUTCString() },
      { label: 'YYYY-MM-DD', value: now.toISOString().split('T')[0] },
      { label: 'YYYY-MM-DD HH:mm:ss', value: formatDateTime(now) }
    ]
  }

  // 快捷时间偏移
  const getQuickTimestamp = (offset) => {
    return Math.floor(Date.now() / 1000) + offset
  }

  // 组件挂载时启动定时器
  onMounted(() => {
    startTimer()
  })

  // 组件卸载时清理定时器
  onUnmounted(() => {
    stopTimer()
  })

  return {
    currentTime,
    currentTimestamp,
    currentTimestampMs,
    currentDateTime,
    timestampToDate,
    dateToTimestamp,
    formatDateTime,
    getRelativeTime,
    getCommonFormats,
    getQuickTimestamp,
    startTimer,
    stopTimer
  }
}
