<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大小写转换器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 手绘素描风格 */
        body {
            background: linear-gradient(135deg, #fef7cd 0%, #fde047 100%);
            font-family: 'Comic Sans MS', cursive, sans-serif;
        }
        
        .sketch-border {
            border: 2px solid #eab308;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            position: relative;
        }
        
        .sketch-border::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 1px dashed #facc15;
            border-radius: 14px;
            pointer-events: none;
        }
        
        .btn-sketch {
            background: linear-gradient(145deg, #eab308, #ca8a04);
            border: 2px solid #a16207;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-sketch:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(234, 179, 8, 0.3);
        }
        
        .btn-sketch:active {
            transform: translateY(0);
        }
        
        .textarea-sketch {
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            background: white;
            transition: border-color 0.3s ease;
            resize: vertical;
            min-height: 120px;
        }
        
        .textarea-sketch:focus {
            outline: none;
            border-color: #eab308;
            box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }
        
        .case-card {
            background: linear-gradient(145deg, #fffbeb, #fef3c7);
            border: 2px solid #facc15;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .case-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(234, 179, 8, 0.2);
        }
        
        .case-display {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #1f2937;
            background: white;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            word-break: break-all;
            min-height: 80px;
            white-space: pre-wrap;
        }
        
        .error-text {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        .success-text {
            color: #059669;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        /* 装饰元素 */
        .doodle-star {
            position: absolute;
            color: #eab308;
            font-size: 20px;
            animation: sparkle 2s ease-in-out infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { opacity: 1; transform: scale(1) rotate(0deg); }
            50% { opacity: 0.7; transform: scale(1.1) rotate(180deg); }
        }
        
        .text-icon {
            stroke: #eab308;
            stroke-width: 2;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
            animation: flow 2s linear infinite;
        }
        
        @keyframes flow {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -20; }
        }
        
        .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #eab308;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: #ca8a04;
            transform: scale(1.05);
        }
        
        .case-label {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .case-info {
            font-size: 12px;
            color: #6b7280;
            font-weight: normal;
        }
        
        .stats-card {
            background: white;
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }
        
        .stats-number {
            font-size: 24px;
            font-weight: bold;
            color: #eab308;
        }
        
        .stats-label {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }
        
        .quick-action {
            background: white;
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 8px 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }
        
        .quick-action:hover {
            background: #fef3c7;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(234, 179, 8, 0.2);
        }
        
        .format-preview {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #475569;
            margin-top: 4px;
        }
        
        .transform-btn {
            background: linear-gradient(145deg, #3b82f6, #2563eb);
            border: 2px solid #1d4ed8;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            padding: 8px 16px;
            transition: all 0.3s ease;
            font-size: 14px;
            cursor: pointer;
        }
        
        .transform-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        }
        
        .input-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
            margin-top: 12px;
        }
        
        .char-highlight {
            background: #fef3c7;
            padding: 2px 4px;
            border-radius: 3px;
            border: 1px solid #facc15;
        }
    </style>
</head>
<body class="min-h-screen p-6">
    <div class="max-w-6xl mx-auto">
        <!-- 标题区域 -->
        <div class="text-center mb-8 relative">
            <div class="doodle-star" style="top: 10px; left: 15%;">🔤</div>
            <div class="doodle-star" style="top: 15px; right: 20%; animation-delay: 1s;">✏️</div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">大小写转换器</h1>
            <p class="text-lg text-gray-600">快速转换文本的大小写格式</p>
            
            <!-- 文本图标 -->
            <svg class="absolute top-16 left-1/2 transform -translate-x-1/2" width="80" height="40" viewBox="0 0 80 40">
                <text class="text-icon" x="10" y="25" font-family="serif" font-size="16" stroke-dasharray="2,2">Aa</text>
                <text class="text-icon" x="40" y="25" font-family="serif" font-size="16" stroke-dasharray="2,2" style="animation-delay: 0.5s;">Bb</text>
            </svg>
        </div>
        
        <!-- 输入区域 -->
        <div class="sketch-border bg-white p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <span class="mr-2">📝</span>
                输入文本
            </h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2">
                    <textarea 
                        id="textInput" 
                        class="textarea-sketch w-full" 
                        placeholder="在此输入要转换的文本..."
                        rows="6"
                    ></textarea>
                    
                    <!-- 输入统计 -->
                    <div class="input-stats">
                        <div class="stats-card">
                            <div id="charCount" class="stats-number">0</div>
                            <div class="stats-label">字符数</div>
                        </div>
                        <div class="stats-card">
                            <div id="wordCount" class="stats-number">0</div>
                            <div class="stats-label">单词数</div>
                        </div>
                        <div class="stats-card">
                            <div id="lineCount" class="stats-number">0</div>
                            <div class="stats-label">行数</div>
                        </div>
                        <div class="stats-card">
                            <div id="upperCount" class="stats-number">0</div>
                            <div class="stats-label">大写字母</div>
                        </div>
                        <div class="stats-card">
                            <div id="lowerCount" class="stats-number">0</div>
                            <div class="stats-label">小写字母</div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">快速操作</h3>
                    <div class="space-y-2">
                        <button id="clearBtn" class="btn-sketch w-full">
                            <span class="mr-1">🗑️</span>
                            清空文本
                        </button>
                        <button id="pasteBtn" class="btn-sketch w-full bg-gradient-to-r from-blue-500 to-blue-600 border-blue-700">
                            <span class="mr-1">📋</span>
                            粘贴文本
                        </button>
                        <button id="sampleBtn" class="btn-sketch w-full bg-gradient-to-r from-green-500 to-green-600 border-green-700">
                            <span class="mr-1">📄</span>
                            示例文本
                        </button>
                    </div>
                    
                    <h3 class="text-lg font-semibold text-gray-800 mb-3 mt-6">快速转换</h3>
                    <div class="grid grid-cols-2 gap-2">
                        <div class="quick-action" data-transform="uppercase">
                            <div class="font-medium">全部大写</div>
                            <div class="format-preview">HELLO WORLD</div>
                        </div>
                        <div class="quick-action" data-transform="lowercase">
                            <div class="font-medium">全部小写</div>
                            <div class="format-preview">hello world</div>
                        </div>
                        <div class="quick-action" data-transform="capitalize">
                            <div class="font-medium">首字母大写</div>
                            <div class="format-preview">Hello World</div>
                        </div>
                        <div class="quick-action" data-transform="sentence">
                            <div class="font-medium">句首大写</div>
                            <div class="format-preview">Hello world</div>
                        </div>
                        <div class="quick-action" data-transform="camelCase">
                            <div class="font-medium">驼峰命名</div>
                            <div class="format-preview">helloWorld</div>
                        </div>
                        <div class="quick-action" data-transform="pascalCase">
                            <div class="font-medium">帕斯卡命名</div>
                            <div class="format-preview">HelloWorld</div>
                        </div>
                        <div class="quick-action" data-transform="snakeCase">
                            <div class="font-medium">下划线命名</div>
                            <div class="format-preview">hello_world</div>
                        </div>
                        <div class="quick-action" data-transform="kebabCase">
                            <div class="font-medium">短横线命名</div>
                            <div class="format-preview">hello-world</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 转换结果 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 基础转换 -->
            <div class="sketch-border bg-white p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">🔄</span>
                    基础转换
                </h3>
                
                <div class="space-y-3">
                    <div class="case-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="case-label">
                            全部大写 (UPPERCASE)
                            <span class="case-info">所有字母转为大写</span>
                        </div>
                        <div id="uppercaseResult" class="case-display">在左侧输入文本...</div>
                    </div>
                    
                    <div class="case-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="case-label">
                            全部小写 (lowercase)
                            <span class="case-info">所有字母转为小写</span>
                        </div>
                        <div id="lowercaseResult" class="case-display">在左侧输入文本...</div>
                    </div>
                    
                    <div class="case-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="case-label">
                            首字母大写 (Title Case)
                            <span class="case-info">每个单词首字母大写</span>
                        </div>
                        <div id="titleResult" class="case-display">在左侧输入文本...</div>
                    </div>
                    
                    <div class="case-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="case-label">
                            句首大写 (Sentence case)
                            <span class="case-info">每句话首字母大写</span>
                        </div>
                        <div id="sentenceResult" class="case-display">在左侧输入文本...</div>
                    </div>
                </div>
            </div>
            
            <!-- 编程命名转换 -->
            <div class="sketch-border bg-white p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">💻</span>
                    编程命名转换
                </h3>
                
                <div class="space-y-3">
                    <div class="case-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="case-label">
                            驼峰命名 (camelCase)
                            <span class="case-info">首字母小写，后续单词首字母大写</span>
                        </div>
                        <div id="camelResult" class="case-display">在左侧输入文本...</div>
                    </div>
                    
                    <div class="case-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="case-label">
                            帕斯卡命名 (PascalCase)
                            <span class="case-info">所有单词首字母大写</span>
                        </div>
                        <div id="pascalResult" class="case-display">在左侧输入文本...</div>
                    </div>
                    
                    <div class="case-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="case-label">
                            下划线命名 (snake_case)
                            <span class="case-info">单词间用下划线连接</span>
                        </div>
                        <div id="snakeResult" class="case-display">在左侧输入文本...</div>
                    </div>
                    
                    <div class="case-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="case-label">
                            短横线命名 (kebab-case)
                            <span class="case-info">单词间用短横线连接</span>
                        </div>
                        <div id="kebabResult" class="case-display">在左侧输入文本...</div>
                    </div>
                    
                    <div class="case-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="case-label">
                            常量命名 (CONSTANT_CASE)
                            <span class="case-info">全大写，下划线分隔</span>
                        </div>
                        <div id="constantResult" class="case-display">在左侧输入文本...</div>
                    </div>
                    
                    <div class="case-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="case-label">
                            点分命名 (dot.case)
                            <span class="case-info">单词间用点号连接</span>
                        </div>
                        <div id="dotResult" class="case-display">在左侧输入文本...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 特殊转换 -->
        <div class="mt-6 sketch-border bg-white p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <span class="mr-2">✨</span>
                特殊转换
            </h3>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <div class="case-card">
                    <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                    <div class="case-label">
                        反转大小写 (iNVERT cASE)
                        <span class="case-info">大写变小写，小写变大写</span>
                    </div>
                    <div id="invertResult" class="case-display">在左侧输入文本...</div>
                </div>
                
                <div class="case-card">
                    <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                    <div class="case-label">
                        随机大小写 (RaNdOm CaSe)
                        <span class="case-info">随机大小写混合</span>
                    </div>
                    <div id="randomResult" class="case-display">在左侧输入文本...</div>
                    <button class="transform-btn mt-2" onclick="caseConverter.generateRandom()">
                        🎲 重新随机
                    </button>
                </div>
                
                <div class="case-card">
                    <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                    <div class="case-label">
                        交替大小写 (AlTeRnAtInG)
                        <span class="case-info">字母交替大小写</span>
                    </div>
                    <div id="alternatingResult" class="case-display">在左侧输入文本...</div>
                </div>
            </div>
        </div>
        
        <!-- 状态信息 -->
        <div id="statusInfo" class="mt-4"></div>
    </div>
    
    <script>
        class CaseConverter {
            constructor() {
                this.initElements();
                this.bindEvents();
                this.sampleTexts = [
                    "Hello World! This is a sample text.",
                    "the quick brown fox jumps over the lazy dog",
                    "JavaScript is a programming language",
                    "convert this text to different cases",
                    "Welcome to the Case Converter Tool!"
                ];
            }
            
            initElements() {
                // 输入元素
                this.textInput = document.getElementById('textInput');
                
                // 按钮
                this.clearBtn = document.getElementById('clearBtn');
                this.pasteBtn = document.getElementById('pasteBtn');
                this.sampleBtn = document.getElementById('sampleBtn');
                
                // 统计元素
                this.charCount = document.getElementById('charCount');
                this.wordCount = document.getElementById('wordCount');
                this.lineCount = document.getElementById('lineCount');
                this.upperCount = document.getElementById('upperCount');
                this.lowerCount = document.getElementById('lowerCount');
                
                // 结果显示元素
                this.results = {
                    uppercase: document.getElementById('uppercaseResult'),
                    lowercase: document.getElementById('lowercaseResult'),
                    title: document.getElementById('titleResult'),
                    sentence: document.getElementById('sentenceResult'),
                    camel: document.getElementById('camelResult'),
                    pascal: document.getElementById('pascalResult'),
                    snake: document.getElementById('snakeResult'),
                    kebab: document.getElementById('kebabResult'),
                    constant: document.getElementById('constantResult'),
                    dot: document.getElementById('dotResult'),
                    invert: document.getElementById('invertResult'),
                    random: document.getElementById('randomResult'),
                    alternating: document.getElementById('alternatingResult')
                };
                
                this.statusInfo = document.getElementById('statusInfo');
            }
            
            bindEvents() {
                // 输入监听
                this.textInput.addEventListener('input', () => {
                    this.updateStats();
                    this.convertAll();
                });
                
                // 按钮事件
                this.clearBtn.addEventListener('click', () => this.clearText());
                this.pasteBtn.addEventListener('click', () => this.pasteText());
                this.sampleBtn.addEventListener('click', () => this.loadSample());
                
                // 快速转换
                document.querySelectorAll('.quick-action').forEach(action => {
                    action.addEventListener('click', (e) => {
                        const transform = e.currentTarget.dataset.transform;
                        this.applyQuickTransform(transform);
                    });
                });
            }
            
            updateStats() {
                const text = this.textInput.value;
                
                // 字符数
                this.charCount.textContent = text.length;
                
                // 单词数
                const words = text.trim() ? text.trim().split(/\s+/) : [];
                this.wordCount.textContent = words.length;
                
                // 行数
                const lines = text ? text.split('\n') : [''];
                this.lineCount.textContent = lines.length;
                
                // 大小写字母数
                const upperMatches = text.match(/[A-Z]/g);
                const lowerMatches = text.match(/[a-z]/g);
                this.upperCount.textContent = upperMatches ? upperMatches.length : 0;
                this.lowerCount.textContent = lowerMatches ? lowerMatches.length : 0;
            }
            
            convertAll() {
                const text = this.textInput.value;
                
                if (!text.trim()) {
                    // 清空所有结果
                    Object.values(this.results).forEach(element => {
                        element.textContent = '在左侧输入文本...';
                    });
                    return;
                }
                
                // 基础转换
                this.results.uppercase.textContent = this.toUpperCase(text);
                this.results.lowercase.textContent = this.toLowerCase(text);
                this.results.title.textContent = this.toTitleCase(text);
                this.results.sentence.textContent = this.toSentenceCase(text);
                
                // 编程命名转换
                this.results.camel.textContent = this.toCamelCase(text);
                this.results.pascal.textContent = this.toPascalCase(text);
                this.results.snake.textContent = this.toSnakeCase(text);
                this.results.kebab.textContent = this.toKebabCase(text);
                this.results.constant.textContent = this.toConstantCase(text);
                this.results.dot.textContent = this.toDotCase(text);
                
                // 特殊转换
                this.results.invert.textContent = this.toInvertCase(text);
                this.results.random.textContent = this.toRandomCase(text);
                this.results.alternating.textContent = this.toAlternatingCase(text);
                
                // 添加复制功能
                this.addCopyFunctionality();
            }
            
            // 基础转换方法
            toUpperCase(text) {
                return text.toUpperCase();
            }
            
            toLowerCase(text) {
                return text.toLowerCase();
            }
            
            toTitleCase(text) {
                return text.replace(/\w\S*/g, (txt) => 
                    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
                );
            }
            
            toSentenceCase(text) {
                return text.toLowerCase().replace(/(^|[.!?]\s+)([a-z])/g, (match, p1, p2) => 
                    p1 + p2.toUpperCase()
                );
            }
            
            // 编程命名转换方法
            toCamelCase(text) {
                return text
                    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => 
                        index === 0 ? word.toLowerCase() : word.toUpperCase()
                    )
                    .replace(/\s+/g, '')
                    .replace(/[^a-zA-Z0-9]/g, '');
            }
            
            toPascalCase(text) {
                return text
                    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => word.toUpperCase())
                    .replace(/\s+/g, '')
                    .replace(/[^a-zA-Z0-9]/g, '');
            }
            
            toSnakeCase(text) {
                return text
                    .replace(/\W+/g, ' ')
                    .split(/ |\s/)
                    .map(word => word.toLowerCase())
                    .filter(word => word.length > 0)
                    .join('_');
            }
            
            toKebabCase(text) {
                return text
                    .replace(/\W+/g, ' ')
                    .split(/ |\s/)
                    .map(word => word.toLowerCase())
                    .filter(word => word.length > 0)
                    .join('-');
            }
            
            toConstantCase(text) {
                return this.toSnakeCase(text).toUpperCase();
            }
            
            toDotCase(text) {
                return text
                    .replace(/\W+/g, ' ')
                    .split(/ |\s/)
                    .map(word => word.toLowerCase())
                    .filter(word => word.length > 0)
                    .join('.');
            }
            
            // 特殊转换方法
            toInvertCase(text) {
                return text.split('').map(char => {
                    if (char >= 'A' && char <= 'Z') {
                        return char.toLowerCase();
                    } else if (char >= 'a' && char <= 'z') {
                        return char.toUpperCase();
                    }
                    return char;
                }).join('');
            }
            
            toRandomCase(text) {
                return text.split('').map(char => {
                    if (/[a-zA-Z]/.test(char)) {
                        return Math.random() > 0.5 ? char.toUpperCase() : char.toLowerCase();
                    }
                    return char;
                }).join('');
            }
            
            toAlternatingCase(text) {
                let isUpper = false;
                return text.split('').map(char => {
                    if (/[a-zA-Z]/.test(char)) {
                        isUpper = !isUpper;
                        return isUpper ? char.toUpperCase() : char.toLowerCase();
                    }
                    return char;
                }).join('');
            }
            
            // 快速转换
            applyQuickTransform(transform) {
                const text = this.textInput.value;
                if (!text.trim()) {
                    this.showStatus('请先输入文本', 'error');
                    return;
                }
                
                let result;
                switch (transform) {
                    case 'uppercase':
                        result = this.toUpperCase(text);
                        break;
                    case 'lowercase':
                        result = this.toLowerCase(text);
                        break;
                    case 'capitalize':
                        result = this.toTitleCase(text);
                        break;
                    case 'sentence':
                        result = this.toSentenceCase(text);
                        break;
                    case 'camelCase':
                        result = this.toCamelCase(text);
                        break;
                    case 'pascalCase':
                        result = this.toPascalCase(text);
                        break;
                    case 'snakeCase':
                        result = this.toSnakeCase(text);
                        break;
                    case 'kebabCase':
                        result = this.toKebabCase(text);
                        break;
                    default:
                        return;
                }
                
                this.textInput.value = result;
                this.updateStats();
                this.convertAll();
                this.showStatus(`已应用${this.getTransformName(transform)}转换`, 'success');
            }
            
            getTransformName(transform) {
                const names = {
                    'uppercase': '全部大写',
                    'lowercase': '全部小写',
                    'capitalize': '首字母大写',
                    'sentence': '句首大写',
                    'camelCase': '驼峰命名',
                    'pascalCase': '帕斯卡命名',
                    'snakeCase': '下划线命名',
                    'kebabCase': '短横线命名'
                };
                return names[transform] || transform;
            }
            
            // 工具方法
            clearText() {
                this.textInput.value = '';
                this.updateStats();
                this.convertAll();
                this.showStatus('已清空文本', 'success');
            }
            
            async pasteText() {
                try {
                    const text = await navigator.clipboard.readText();
                    this.textInput.value = text;
                    this.updateStats();
                    this.convertAll();
                    this.showStatus('已粘贴文本', 'success');
                } catch (error) {
                    this.showStatus('粘贴失败，请手动粘贴', 'error');
                }
            }
            
            loadSample() {
                const randomIndex = Math.floor(Math.random() * this.sampleTexts.length);
                this.textInput.value = this.sampleTexts[randomIndex];
                this.updateStats();
                this.convertAll();
                this.showStatus('已加载示例文本', 'success');
            }
            
            generateRandom() {
                const text = this.textInput.value;
                if (!text.trim()) {
                    this.showStatus('请先输入文本', 'error');
                    return;
                }
                
                this.results.random.textContent = this.toRandomCase(text);
                this.addCopyFunctionality();
                this.showStatus('已重新生成随机大小写', 'success');
            }
            
            addCopyFunctionality() {
                Object.entries(this.results).forEach(([key, element]) => {
                    const container = element.parentElement;
                    const value = element.textContent;
                    
                    if (value && value !== '在左侧输入文本...') {
                        container.copyValue = async () => {
                            try {
                                await navigator.clipboard.writeText(value);
                                this.showStatus('已复制到剪贴板！', 'success');
                                
                                const btn = container.querySelector('.copy-btn');
                                const originalText = btn.textContent;
                                btn.textContent = '✅';
                                setTimeout(() => {
                                    btn.textContent = originalText;
                                }, 2000);
                            } catch (error) {
                                this.showStatus('复制失败，请手动复制', 'error');
                            }
                        };
                    }
                });
            }
            
            showStatus(message, type) {
                const className = type === 'error' ? 'error-text' : 'success-text';
                this.statusInfo.innerHTML = `<div class="${className}">${message}</div>`;
                
                // 3秒后自动清除状态
                setTimeout(() => {
                    this.statusInfo.innerHTML = '';
                }, 3000);
            }
        }
        
        // 初始化应用
        let caseConverter;
        document.addEventListener('DOMContentLoaded', () => {
            caseConverter = new CaseConverter();
        });
    </script>
</body>
</html>