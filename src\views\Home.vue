<template>
  <div class="home-container">
    <!-- 吉卜力主题装饰 -->
    <GhibliDecorations />

    <!-- 头部 -->
    <header class="home-header">
      <div class="header-content">
        <div class="logo-section">
          <div class="logo">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
            </svg>
          </div>
          <h1 class="title">ToolHub</h1>
        </div>
        
        <SearchBar @search="handleSearch" />
        <ThemeToggle />
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="home-main-layout">
      <!-- 侧边栏 -->
      <Sidebar 
        class="home-sidebar"
        :categories="categories" 
        :selected-category="selectedCategory"
        @category-change="handleCategoryChange" 
      />

      <!-- 主内容区 -->
      <main class="home-main-content">
        <div class="home-tools-grid">
          <ToolCard 
            v-for="tool in filteredTools" 
            :key="tool.id" 
            :tool="tool"
          />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import ToolCard from '@/components/ToolCard.vue'
import SearchBar from '@/components/SearchBar.vue'
import Sidebar from '@/components/Sidebar.vue'
import ThemeToggle from '@/components/ThemeToggle.vue'
import GhibliDecorations from '@/components/GhibliDecorations.vue'
import { tools, categories } from '@/data/tools.js'

const selectedCategory = ref('all')
const searchQuery = ref('')

const filteredTools = computed(() => {
  let filtered = tools
  
  // 分类筛选
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(tool => tool.category === selectedCategory.value)
  }
  
  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(tool => 
      tool.name.toLowerCase().includes(query) ||
      tool.description.toLowerCase().includes(query) ||
      tool.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }
  
  return filtered
})

const handleCategoryChange = (category) => {
  selectedCategory.value = category
}

const handleSearch = (query) => {
  searchQuery.value = query
}

// 主题初始化
onMounted(() => {
  const savedTheme = localStorage.getItem('theme') || 'light'
  document.documentElement.setAttribute('data-theme', savedTheme)
})
</script>

<style scoped>
/* 手绘素描风格主页布局 */
.home-container {
  display: grid;
  grid-template-areas:
    "header header"
    "sidebar main";
  grid-template-columns: 280px 1fr;
  grid-template-rows: auto 1fr;
  min-height: 100vh;
  /* 纹理纸质背景 */
  background: 
    radial-gradient(circle at 20% 50%, rgba(183, 223, 186, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 182, 193, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(221, 160, 221, 0.1) 0%, transparent 50%),
    linear-gradient(90deg, rgba(240, 248, 255, 0.3) 1px, transparent 1px),
    linear-gradient(rgba(240, 248, 255, 0.3) 1px, transparent 1px),
    #F7F4E9;
  background-size: 100% 100%, 100% 100%, 100% 100%, 20px 20px, 20px 20px, 100% 100%;
  font-family: 'Comic Sans MS', 'Chalkduster', 'Marker Felt', cursive, sans-serif;
  position: relative;
}

/* 添加纸张纹理效果 */
.home-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(0,0,0,0.02) 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, rgba(0,0,0,0.02) 1px, transparent 1px);
  background-size: 50px 50px, 30px 30px;
  pointer-events: none;
  z-index: 0;
}

/* 头部手绘风格 */
.home-header {
  grid-area: header;
  background: linear-gradient(135deg, #FEFCF7 0%, #F7F4E9 100%);
  border-bottom: 3px dashed #B7DFBA;
  padding: 0;
  z-index: 10;
  position: relative;
  box-shadow: 0 3px 8px rgba(123, 158, 137, 0.15);
}

/* 添加手绘装饰线条 */
.home-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 20px;
  right: 20px;
  height: 2px;
  background: repeating-linear-gradient(
    90deg,
    #B7DFBA 0px,
    #B7DFBA 10px,
    transparent 10px,
    transparent 15px
  );
  transform: rotate(-0.5deg);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.2rem 2rem;
  max-width: 100%;
  position: relative;
  z-index: 1;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;
  transform: rotate(-1deg);
}

.logo {
  width: 40px;
  height: 40px;
  color: #7B9E89;
  background: radial-gradient(circle, rgba(183, 223, 186, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  padding: 6px;
  border: 2px dashed #7B9E89;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-3px) rotate(2deg); }
}

.logo svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(1px 1px 2px rgba(123, 158, 137, 0.3));
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: #7B9E89;
  margin: 0;
  text-shadow: 2px 2px 0px rgba(183, 223, 186, 0.3);
  transform: rotate(1deg);
  position: relative;
}

/* 添加手绘下划线效果 */
.title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    #FFB6C1 20%, 
    #DDA0DD 50%, 
    #FFFFE0 80%, 
    transparent 100%
  );
  border-radius: 50px;
  transform: rotate(-1deg);
}

/* 主要布局区域 */
.home-main-layout {
  display: contents;
  position: relative;
  z-index: 1;
}

/* 侧边栏手绘风格 */
.home-sidebar {
  grid-area: sidebar;
  background: linear-gradient(180deg, #FEFCF7 0%, #F7F4E9 100%);
  border-right: 3px dashed #B7DFBA;
  padding: 2rem 1.5rem;
  overflow-y: auto;
  position: relative;
  box-shadow: inset -3px 0 8px rgba(123, 158, 137, 0.1);
}

/* 侧边栏装饰 */
.home-sidebar::before {
  content: '📋';
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  opacity: 0.3;
  transform: rotate(15deg);
}

/* 主内容区手绘风格 */
.home-main-content {
  grid-area: main;
  padding: 2rem;
  overflow-y: auto;
  background: 
    radial-gradient(circle at 30% 30%, rgba(183, 223, 186, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(255, 182, 193, 0.05) 0%, transparent 50%),
    #F7F4E9;
  position: relative;
}

/* 添加浮动装饰元素 */
.home-main-content::before {
  content: '✨';
  position: absolute;
  top: 2rem;
  right: 3rem;
  font-size: 1.2rem;
  opacity: 0.4;
  animation: sparkle 4s ease-in-out infinite;
}

.home-main-content::after {
  content: '🎨';
  position: absolute;
  bottom: 3rem;
  left: 3rem;
  font-size: 1.2rem;
  opacity: 0.4;
  animation: bounce 3s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.4; }
  50% { transform: scale(1.2) rotate(180deg); opacity: 0.7; }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

/* 工具网格手绘风格 */
.home-tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 100%;
  position: relative;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-container {
    grid-template-areas:
      "header"
      "main";
    grid-template-columns: 1fr;
  }
  
  .home-sidebar {
    display: none;
  }
  
  .header-content {
    padding: 1rem;
  }
  
  .home-tools-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .title {
    font-size: 1.5rem;
  }
}

@media (max-width: 1024px) {
  .home-container {
    grid-template-columns: 240px 1fr;
  }
  
  .home-tools-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  }
}

/* 确保工具卡片不会溢出 */
.home-tools-grid > * {
  min-width: 0;
}

/* 添加整体动画效果 */
.home-container {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>