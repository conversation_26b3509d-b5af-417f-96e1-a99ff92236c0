<template>
  <svg 
    class="icon-svg" 
    viewBox="0 0 100 100" 
    xmlns="http://www.w3.org/2000/svg"
  >
    <!-- 背景圆圈 -->
    <circle 
      cx="50" 
      cy="50" 
      r="45" 
      fill="#fef3c7" 
      stroke="#f59e0b" 
      stroke-width="2"
      class="bg-circle"
    />
    
    <!-- QR码主体框架 -->
    <rect 
      x="25" 
      y="25" 
      width="50" 
      height="50" 
      rx="3" 
      fill="#d97706" 
      stroke="#92400e" 
      stroke-width="2"
      class="qr-frame"
    />
    
    <!-- QR码内容区域 -->
    <rect 
      x="28" 
      y="28" 
      width="44" 
      height="44" 
      rx="2" 
      fill="#fef3c7" 
      class="qr-content"
    />
    
    <!-- 左上角定位点 -->
    <g class="position-marker top-left">
      <rect x="30" y="30" width="12" height="12" fill="#92400e" class="marker-outer" />
      <rect x="32" y="32" width="8" height="8" fill="#fef3c7" class="marker-middle" />
      <rect x="34" y="34" width="4" height="4" fill="#92400e" class="marker-inner" />
    </g>
    
    <!-- 右上角定位点 -->
    <g class="position-marker top-right">
      <rect x="58" y="30" width="12" height="12" fill="#92400e" class="marker-outer" />
      <rect x="60" y="32" width="8" height="8" fill="#fef3c7" class="marker-middle" />
      <rect x="62" y="34" width="4" height="4" fill="#92400e" class="marker-inner" />
    </g>
    
    <!-- 左下角定位点 -->
    <g class="position-marker bottom-left">
      <rect x="30" y="58" width="12" height="12" fill="#92400e" class="marker-outer" />
      <rect x="32" y="60" width="8" height="8" fill="#fef3c7" class="marker-middle" />
      <rect x="34" y="62" width="4" height="4" fill="#92400e" class="marker-inner" />
    </g>
    
    <!-- QR码数据模块 -->
    <g class="data-modules">
      <!-- 第一行 -->
      <rect x="46" y="30" width="2" height="2" fill="#92400e" class="module m1" />
      <rect x="50" y="30" width="2" height="2" fill="#92400e" class="module m2" />
      <rect x="54" y="30" width="2" height="2" fill="#92400e" class="module m3" />
      
      <!-- 第二行 -->
      <rect x="44" y="34" width="2" height="2" fill="#92400e" class="module m4" />
      <rect x="48" y="34" width="2" height="2" fill="#92400e" class="module m5" />
      <rect x="52" y="34" width="2" height="2" fill="#92400e" class="module m6" />
      <rect x="56" y="34" width="2" height="2" fill="#92400e" class="module m7" />
      
      <!-- 第三行 -->
      <rect x="46" y="38" width="2" height="2" fill="#92400e" class="module m8" />
      <rect x="50" y="38" width="2" height="2" fill="#92400e" class="module m9" />
      <rect x="54" y="38" width="2" height="2" fill="#92400e" class="module m10" />
      
      <!-- 中间区域 -->
      <rect x="44" y="46" width="2" height="2" fill="#92400e" class="module m11" />
      <rect x="48" y="46" width="2" height="2" fill="#92400e" class="module m12" />
      <rect x="52" y="46" width="2" height="2" fill="#92400e" class="module m13" />
      <rect x="56" y="46" width="2" height="2" fill="#92400e" class="module m14" />
      
      <!-- 下方区域 -->
      <rect x="46" y="54" width="2" height="2" fill="#92400e" class="module m15" />
      <rect x="50" y="54" width="2" height="2" fill="#92400e" class="module m16" />
      <rect x="54" y="54" width="2" height="2" fill="#92400e" class="module m17" />
      
      <rect x="44" y="58" width="2" height="2" fill="#92400e" class="module m18" />
      <rect x="48" y="58" width="2" height="2" fill="#92400e" class="module m19" />
      <rect x="52" y="58" width="2" height="2" fill="#92400e" class="module m20" />
      
      <rect x="46" y="62" width="2" height="2" fill="#92400e" class="module m21" />
      <rect x="50" y="62" width="2" height="2" fill="#92400e" class="module m22" />
      
      <rect x="44" y="66" width="2" height="2" fill="#92400e" class="module m23" />
      <rect x="48" y="66" width="2" height="2" fill="#92400e" class="module m24" />
      <rect x="52" y="66" width="2" height="2" fill="#92400e" class="module m25" />
    </g>
    
    <!-- QR标签 -->
    <text x="50" y="85" text-anchor="middle" font-family="monospace" font-size="8" fill="#92400e" font-weight="bold" class="qr-label">QR CODE</text>
    
    <!-- 扫描线效果 -->
    <g class="scan-effects">
      <line x1="20" y1="50" x2="80" y2="50" stroke="#f59e0b" stroke-width="2" opacity="0.7" class="scan-line horizontal" />
      <line x1="50" y1="20" x2="50" y2="80" stroke="#f59e0b" stroke-width="2" opacity="0.7" class="scan-line vertical" />
    </g>
    
    <!-- 装饰性数据点 -->
    <g class="data-points">
      <circle cx="15" cy="30" r="1.5" fill="#fbbf24" class="data-point dp1" />
      <circle cx="85" cy="35" r="1.5" fill="#fbbf24" class="data-point dp2" />
      <circle cx="20" cy="70" r="1.5" fill="#fbbf24" class="data-point dp3" />
      <circle cx="80" cy="65" r="1.5" fill="#fbbf24" class="data-point dp4" />
    </g>
    
    <!-- 生成效果 -->
    <g class="generation-effect">
      <circle cx="50" cy="50" r="35" fill="none" stroke="#fbbf24" stroke-width="1" opacity="0.5" class="effect-ring ring1" />
      <circle cx="50" cy="50" r="40" fill="none" stroke="#fbbf24" stroke-width="1" opacity="0.3" class="effect-ring ring2" />
    </g>
  </svg>
</template>

<style scoped>
.icon-svg {
  width: 60px;
  height: 60px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.icon-svg:hover {
  transform: scale(1.1) rotate(5deg);
}

.icon-svg:hover .bg-circle {
  fill: #fde68a;
  stroke: #d97706;
  animation: pulse 1s infinite;
}

.icon-svg:hover .qr-frame {
  animation: frameGlow 1.5s ease-in-out infinite;
}

.icon-svg:hover .qr-content {
  animation: contentFlicker 2s ease-in-out infinite;
}

.icon-svg:hover .position-marker {
  animation: markerPulse 1.2s ease-in-out infinite;
}

.icon-svg:hover .top-left {
  animation-delay: 0s;
}

.icon-svg:hover .top-right {
  animation-delay: 0.4s;
}

.icon-svg:hover .bottom-left {
  animation-delay: 0.8s;
}

.icon-svg:hover .module {
  animation: moduleGenerate 3s ease-in-out infinite;
}

.icon-svg:hover .m1 {
  animation-delay: 0.1s;
}

.icon-svg:hover .m2 {
  animation-delay: 0.2s;
}

.icon-svg:hover .m3 {
  animation-delay: 0.3s;
}

.icon-svg:hover .m4 {
  animation-delay: 0.4s;
}

.icon-svg:hover .m5 {
  animation-delay: 0.5s;
}

.icon-svg:hover .m6 {
  animation-delay: 0.6s;
}

.icon-svg:hover .m7 {
  animation-delay: 0.7s;
}

.icon-svg:hover .m8 {
  animation-delay: 0.8s;
}

.icon-svg:hover .m9 {
  animation-delay: 0.9s;
}

.icon-svg:hover .m10 {
  animation-delay: 1s;
}

.icon-svg:hover .m11 {
  animation-delay: 1.1s;
}

.icon-svg:hover .m12 {
  animation-delay: 1.2s;
}

.icon-svg:hover .m13 {
  animation-delay: 1.3s;
}

.icon-svg:hover .m14 {
  animation-delay: 1.4s;
}

.icon-svg:hover .m15 {
  animation-delay: 1.5s;
}

.icon-svg:hover .m16 {
  animation-delay: 1.6s;
}

.icon-svg:hover .m17 {
  animation-delay: 1.7s;
}

.icon-svg:hover .m18 {
  animation-delay: 1.8s;
}

.icon-svg:hover .m19 {
  animation-delay: 1.9s;
}

.icon-svg:hover .m20 {
  animation-delay: 2s;
}

.icon-svg:hover .m21 {
  animation-delay: 2.1s;
}

.icon-svg:hover .m22 {
  animation-delay: 2.2s;
}

.icon-svg:hover .m23 {
  animation-delay: 2.3s;
}

.icon-svg:hover .m24 {
  animation-delay: 2.4s;
}

.icon-svg:hover .m25 {
  animation-delay: 2.5s;
}

.icon-svg:hover .qr-label {
  animation: labelPulse 1s ease-in-out infinite;
}

.icon-svg:hover .scan-line {
  animation: scanMove 2s linear infinite;
}

.icon-svg:hover .horizontal {
  animation-delay: 0s;
}

.icon-svg:hover .vertical {
  animation-delay: 1s;
}

.icon-svg:hover .data-point {
  animation: pointOrbit 4s ease-in-out infinite;
}

.icon-svg:hover .dp1 {
  animation-delay: 0s;
}

.icon-svg:hover .dp2 {
  animation-delay: 1s;
}

.icon-svg:hover .dp3 {
  animation-delay: 2s;
}

.icon-svg:hover .dp4 {
  animation-delay: 3s;
}

.icon-svg:hover .effect-ring {
  animation: ringExpand 2s ease-out infinite;
}

.icon-svg:hover .ring1 {
  animation-delay: 0s;
}

.icon-svg:hover .ring2 {
  animation-delay: 0.5s;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes frameGlow {
  0%, 100% { filter: drop-shadow(0 0 5px #f59e0b); }
  50% { filter: drop-shadow(0 0 15px #fbbf24); }
}

@keyframes contentFlicker {
  0%, 100% { fill: #fef3c7; }
  50% { fill: #fde68a; }
}

@keyframes markerPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes moduleGenerate {
  0%, 90%, 100% { opacity: 1; fill: #92400e; }
  5%, 85% { opacity: 0; fill: #fbbf24; }
  10%, 80% { opacity: 1; fill: #f59e0b; }
  15%, 75% { opacity: 0; fill: #92400e; }
  20%, 70% { opacity: 1; fill: #92400e; }
}

@keyframes labelPulse {
  0%, 100% { font-size: 8px; }
  50% { font-size: 9px; }
}

@keyframes scanMove {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

@keyframes pointOrbit {
  0%, 100% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.5) rotate(90deg); }
  50% { transform: scale(1) rotate(180deg); }
  75% { transform: scale(0.8) rotate(270deg); }
}

@keyframes ringExpand {
  0% { r: 25; opacity: 0.8; }
  100% { r: 45; opacity: 0; }
}
</style>