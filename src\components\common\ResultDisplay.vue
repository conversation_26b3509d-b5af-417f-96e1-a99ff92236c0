<template>
  <div class="result-display">
    <!-- 单个结果 -->
    <div v-if="!isMultiple" class="result-card">
      <button v-if="allowCopy" class="copy-btn" @click="copyToClipboard(content)" :title="copyButtonText">
        {{ copyIcon }}
      </button>
      
      <div v-if="label" class="result-label">{{ label }}</div>
      
      <div class="result-content" :class="contentClass">
        <!-- 文本内容 -->
        <div v-if="type === 'text'" class="text-content">{{ content }}</div>
        
        <!-- JSON 内容 -->
        <pre v-else-if="type === 'json'" class="json-content">{{ formattedJson }}</pre>
        
        <!-- HTML 内容 -->
        <div v-else-if="type === 'html'" class="html-content" v-html="content"></div>
        
        <!-- 代码内容 -->
        <pre v-else-if="type === 'code'" class="code-content"><code>{{ content }}</code></pre>
        
        <!-- 时间戳显示 -->
        <div v-else-if="type === 'timestamp'" class="timestamp-display">{{ content }}</div>
        
        <!-- 默认文本 -->
        <div v-else class="text-content">{{ content }}</div>
      </div>
    </div>
    
    <!-- 多个结果 -->
    <div v-else class="results-grid">
      <div 
        v-for="(item, index) in results" 
        :key="index" 
        class="result-card"
        :class="{ 'result-card-small': size === 'small' }"
      >
        <button v-if="allowCopy" class="copy-btn" @click="copyToClipboard(item.value)" :title="copyButtonText">
          {{ copyIcon }}
        </button>
        
        <div v-if="item.label" class="result-label">{{ item.label }}</div>
        
        <div class="result-content" :class="getContentClass(item.type)">
          <!-- 根据类型渲染内容 -->
          <div v-if="item.type === 'text'" class="text-content">{{ item.value }}</div>
          <pre v-else-if="item.type === 'json'" class="json-content">{{ formatJson(item.value) }}</pre>
          <div v-else-if="item.type === 'html'" class="html-content" v-html="item.value"></div>
          <pre v-else-if="item.type === 'code'" class="code-content"><code>{{ item.value }}</code></pre>
          <div v-else-if="item.type === 'timestamp'" class="timestamp-display">{{ item.value }}</div>
          <div v-else class="text-content">{{ item.value }}</div>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-if="showEmpty" class="empty-state">
      <div class="empty-icon">📝</div>
      <div class="empty-text">{{ emptyText }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  // 单个结果模式
  content: {
    type: [String, Object, Array],
    default: ''
  },
  type: {
    type: String,
    default: 'text',
    validator: (value) => ['text', 'json', 'html', 'code', 'timestamp'].includes(value)
  },
  label: {
    type: String,
    default: ''
  },
  
  // 多个结果模式
  results: {
    type: Array,
    default: () => []
  },
  
  // 通用配置
  allowCopy: {
    type: Boolean,
    default: true
  },
  size: {
    type: String,
    default: 'normal',
    validator: (value) => ['small', 'normal', 'large'].includes(value)
  },
  emptyText: {
    type: String,
    default: '暂无结果'
  }
})

const emit = defineEmits(['copy'])

const copyIcon = ref('📋')
const copyButtonText = ref('复制')

const isMultiple = computed(() => props.results && props.results.length > 0)
const showEmpty = computed(() => !isMultiple.value && !props.content)

const contentClass = computed(() => {
  const classes = []
  if (props.size === 'small') classes.push('content-small')
  if (props.size === 'large') classes.push('content-large')
  return classes.join(' ')
})

const formattedJson = computed(() => {
  if (props.type === 'json' && props.content) {
    try {
      const parsed = typeof props.content === 'string' ? JSON.parse(props.content) : props.content
      return JSON.stringify(parsed, null, 2)
    } catch (e) {
      return props.content
    }
  }
  return props.content
})

const formatJson = (content) => {
  try {
    const parsed = typeof content === 'string' ? JSON.parse(content) : content
    return JSON.stringify(parsed, null, 2)
  } catch (e) {
    return content
  }
}

const getContentClass = (type) => {
  const classes = []
  if (props.size === 'small') classes.push('content-small')
  if (props.size === 'large') classes.push('content-large')
  return classes.join(' ')
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    
    // 视觉反馈
    copyIcon.value = '✅'
    copyButtonText.value = '已复制'
    
    setTimeout(() => {
      copyIcon.value = '📋'
      copyButtonText.value = '复制'
    }, 2000)
    
    emit('copy', text)
  } catch (error) {
    console.error('复制失败:', error)
    copyIcon.value = '❌'
    copyButtonText.value = '复制失败'
    
    setTimeout(() => {
      copyIcon.value = '📋'
      copyButtonText.value = '复制'
    }, 2000)
  }
}
</script>

<style scoped>
.result-display {
  width: 100%;
}

.result-card {
  background: linear-gradient(145deg, #fffbeb, #fef3c7);
  border: 2px solid #facc15;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  position: relative;
  transition: all 0.3s ease;
}

.result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(234, 179, 8, 0.2);
}

.result-card-small {
  padding: 12px;
  margin-bottom: 8px;
}

.copy-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #eab308;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.copy-btn:hover {
  background: #ca8a04;
  transform: scale(1.05);
}

.result-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.result-content {
  word-break: break-all;
  overflow-wrap: break-word;
}

.text-content {
  font-size: 14px;
  color: #1f2937;
  line-height: 1.5;
}

.json-content {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #1f2937;
  background: white;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  overflow-x: auto;
  white-space: pre-wrap;
}

.html-content {
  font-size: 14px;
  color: #1f2937;
}

.code-content {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #1f2937;
  background: white;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  overflow-x: auto;
}

.timestamp-display {
  font-family: 'Courier New', monospace;
  font-size: 16px;
  font-weight: bold;
  color: #1f2937;
  background: white;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  text-align: center;
}

.content-small .text-content,
.content-small .timestamp-display {
  font-size: 12px;
}

.content-large .text-content,
.content-large .timestamp-display {
  font-size: 18px;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-text {
  font-size: 1.1rem;
}

/* 响应式 */
@media (max-width: 768px) {
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  .result-card {
    padding: 12px;
  }
  
  .copy-btn {
    position: static;
    margin-bottom: 8px;
    display: inline-block;
  }
}
</style>
