# Vue 组件开发指南

## 概述

ToolHub 项目已统一采用 Vue 3 + Composition API 进行工具开发。本指南提供了详细的 Vue 组件开发规范和最佳实践。

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite 4.x
- **样式框架**: Tailwind CSS v4.1
- **路由管理**: Vue Router 4.x
- **组件架构**: Vue 单文件组件 (SFC)
- **测试框架**: Playwright (E2E)
- **代码规范**: ESLint + Prettier
- **包管理**: npm

## 项目结构

```
src/
├── components/
│   ├── common/              # 通用组件
│   │   ├── ToolLayout.vue   # 工具布局组件
│   │   ├── FileUpload.vue   # 文件上传组件
│   │   ├── CodeEditor.vue   # 代码编辑器组件
│   │   ├── ResultDisplay.vue # 结果显示组件
│   │   └── LanguageSelector.vue # 语言选择器
│   ├── tools/               # 工具组件
│   │   ├── development/     # 开发工具
│   │   ├── encoding/        # 编码工具
│   │   ├── conversion/      # 转换工具
│   │   ├── text/           # 文本工具
│   │   ├── image/          # 图片工具
│   │   └── ...
│   └── ui/                 # UI组件
├── composables/            # 组合式函数
│   ├── useFileHandler.js   # 文件处理
│   ├── useClipboard.js     # 剪贴板操作
│   ├── useLocalStorage.js  # 本地存储
│   ├── useToolHistory.js   # 工具历史
│   └── useTheme.js         # 主题管理
├── utils/                  # 工具函数
│   ├── crypto.js           # 加密工具
│   ├── converters.js       # 转换工具
│   ├── validators.js       # 验证工具
│   └── formatters.js       # 格式化工具
├── locales/               # 国际化语言包
│   ├── zh-CN.json         # 简体中文
│   ├── en-US.json         # 英语
│   └── index.js           # 语言包配置
└── data/
    ├── tools.js           # 工具数据配置
    ├── categories.js      # 分类数据
    └── tool-manifest.js   # 工具清单
```

## Vue 组件开发规范

### 1. 组件命名规范

- 使用 PascalCase 命名组件文件
- 组件名应该清晰描述其功能
- 工具组件按分类组织

```javascript
// 示例
JsonFormatter.vue      // JSON格式化器
Base64Encoder.vue     // Base64编码器
TimestampConverter.vue // 时间戳转换器
```

### 2. 组件结构模板

```vue
<template>
  <ToolLayout
    :title="$t('tools.jsonFormatter.title')"
    :description="$t('tools.jsonFormatter.description')"
  >
    <!-- 工具主要内容 -->
    <div class="space-y-6">
      <!-- 输入区域 -->
      <div class="input-section">
        <label class="block text-sm font-medium mb-2">
          {{ $t('common.input') }}
        </label>
        <textarea
          v-model="inputText"
          class="w-full h-40 p-3 border rounded-lg"
          :placeholder="$t('tools.jsonFormatter.inputPlaceholder')"
        />
      </div>

      <!-- 操作按钮 -->
      <div class="flex gap-3">
        <button
          @click="formatJson"
          class="btn-primary"
          :disabled="!inputText.trim()"
        >
          {{ $t('tools.jsonFormatter.format') }}
        </button>
        <button
          @click="clearAll"
          class="btn-secondary"
        >
          {{ $t('common.clear') }}
        </button>
      </div>

      <!-- 结果区域 -->
      <ResultDisplay
        v-if="result"
        :content="result"
        :type="'json'"
        :allow-copy="true"
      />
    </div>
  </ToolLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import ToolLayout from '@/components/common/ToolLayout.vue'
import ResultDisplay from '@/components/common/ResultDisplay.vue'
import { useClipboard } from '@/composables/useClipboard'
import { useToolHistory } from '@/composables/useToolHistory'

// 国际化
const { t } = useI18n()

// 响应式数据
const inputText = ref('')
const result = ref('')
const error = ref('')

// 组合式函数
const { copyToClipboard } = useClipboard()
const { addToHistory } = useToolHistory()

// 计算属性
const isValidJson = computed(() => {
  try {
    JSON.parse(inputText.value)
    return true
  } catch {
    return false
  }
})

// 方法
const formatJson = () => {
  try {
    const parsed = JSON.parse(inputText.value)
    result.value = JSON.stringify(parsed, null, 2)
    error.value = ''
    
    // 添加到历史记录
    addToHistory({
      tool: 'json-formatter',
      input: inputText.value,
      output: result.value,
      timestamp: Date.now()
    })
  } catch (err) {
    error.value = t('tools.jsonFormatter.invalidJson')
    result.value = ''
  }
}

const clearAll = () => {
  inputText.value = ''
  result.value = ''
  error.value = ''
}
</script>

<style scoped>
.btn-primary {
  @apply bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors;
}

.input-section {
  @apply bg-white dark:bg-gray-800 p-4 rounded-lg border;
}
</style>
```

### 3. 组合式函数开发

#### useFileHandler.js
```javascript
import { ref } from 'vue'

export function useFileHandler() {
  const isLoading = ref(false)
  const error = ref('')

  const readFile = (file, type = 'text') => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        resolve(e.target.result)
      }
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      
      if (type === 'text') {
        reader.readAsText(file)
      } else if (type === 'dataURL') {
        reader.readAsDataURL(file)
      } else if (type === 'arrayBuffer') {
        reader.readAsArrayBuffer(file)
      }
    })
  }

  const downloadFile = (content, filename, type = 'text/plain') => {
    const blob = new Blob([content], { type })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return {
    isLoading,
    error,
    readFile,
    downloadFile
  }
}
```

#### useClipboard.js
```javascript
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

export function useClipboard() {
  const { t } = useI18n()
  const isSupported = ref(!!navigator.clipboard)

  const copyToClipboard = async (text) => {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(text)
      } else {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
      }
      return { success: true, message: t('common.copySuccess') }
    } catch (error) {
      return { success: false, message: t('common.copyFailed') }
    }
  }

  const readFromClipboard = async () => {
    try {
      if (navigator.clipboard) {
        return await navigator.clipboard.readText()
      }
      return ''
    } catch (error) {
      console.warn('无法读取剪贴板内容')
      return ''
    }
  }

  return {
    isSupported,
    copyToClipboard,
    readFromClipboard
  }
}
```

### 4. 工具注册系统

#### data/tools.js
```javascript
export const toolCategories = [
  {
    id: 'development',
    name: 'tools.categories.development',
    icon: 'code',
    tools: [
      {
        id: 'json-formatter',
        name: 'tools.jsonFormatter.title',
        description: 'tools.jsonFormatter.description',
        component: () => import('@/components/tools/development/JsonFormatter.vue'),
        tags: ['json', 'format', 'validate'],
        featured: true
      },
      {
        id: 'uuid-generator',
        name: 'tools.uuidGenerator.title',
        description: 'tools.uuidGenerator.description',
        component: () => import('@/components/tools/development/UuidGenerator.vue'),
        tags: ['uuid', 'generate', 'unique']
      }
    ]
  },
  {
    id: 'encoding',
    name: 'tools.categories.encoding',
    icon: 'lock',
    tools: [
      {
        id: 'base64-encoder',
        name: 'tools.base64Encoder.title',
        description: 'tools.base64Encoder.description',
        component: () => import('@/components/tools/encoding/Base64Encoder.vue'),
        tags: ['base64', 'encode', 'decode']
      }
    ]
  }
]

// 获取所有工具的扁平化列表
export const getAllTools = () => {
  return toolCategories.reduce((acc, category) => {
    return acc.concat(category.tools.map(tool => ({
      ...tool,
      category: category.id,
      categoryName: category.name
    })))
  }, [])
}

// 根据ID获取工具
export const getToolById = (id) => {
  return getAllTools().find(tool => tool.id === id)
}

// 搜索工具
export const searchTools = (query, category = null) => {
  const tools = getAllTools()
  const lowerQuery = query.toLowerCase()
  
  return tools.filter(tool => {
    const matchesQuery = !query || 
      tool.name.toLowerCase().includes(lowerQuery) ||
      tool.description.toLowerCase().includes(lowerQuery) ||
      tool.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    
    const matchesCategory = !category || tool.category === category
    
    return matchesQuery && matchesCategory
  })
}
```

### 5. 路由配置

#### router/index.js
```javascript
import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import ToolContainer from '@/views/ToolContainer.vue'
import { getToolById } from '@/data/tools.js'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/tool/:id',
    name: 'Tool',
    component: ToolContainer,
    props: true,
    beforeEnter: (to, from, next) => {
      const tool = getToolById(to.params.id)
      if (tool) {
        next()
      } else {
        next({ name: 'Home' })
      }
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
```

### 6. 国际化配置

#### locales/zh-CN.json
```json
{
  "common": {
    "input": "输入",
    "output": "输出",
    "clear": "清空",
    "copy": "复制",
    "download": "下载",
    "upload": "上传",
    "copySuccess": "复制成功",
    "copyFailed": "复制失败"
  },
  "tools": {
    "categories": {
      "development": "开发工具",
      "encoding": "编码加密",
      "conversion": "转换工具",
      "text": "文本工具",
      "image": "图片工具"
    },
    "jsonFormatter": {
      "title": "JSON格式化器",
      "description": "格式化、压缩和验证JSON数据",
      "inputPlaceholder": "请输入JSON数据...",
      "format": "格式化",
      "compress": "压缩",
      "validate": "验证",
      "invalidJson": "无效的JSON格式"
    }
  }
}
```

## 开发最佳实践

### 1. 代码规范

- 使用 Composition API
- 遵循 Vue 3 官方风格指南
- 使用 TypeScript（推荐）
- 组件职责单一
- 合理使用组合式函数

### 2. 性能优化

- 使用 `defineAsyncComponent` 进行组件懒加载
- 合理使用 `computed` 和 `watch`
- 避免在模板中使用复杂表达式
- 使用 `v-memo` 优化列表渲染

### 3. 测试策略

- 编写单元测试（Vue Test Utils + Vitest）
- E2E测试（Playwright）
- 组件快照测试
- 工具功能验证测试

### 4. 部署和构建

- 使用 Vite 进行构建优化
- 配置代码分割
- 静态资源优化
- PWA 支持

## 迁移指南

### 从HTML工具迁移到Vue组件

1. **分析现有HTML工具功能**
2. **创建对应的Vue组件**
3. **提取可复用的逻辑到组合式函数**
4. **更新工具注册表**
5. **添加国际化支持**
6. **编写测试用例**
7. **更新文档**

### 示例迁移步骤

```bash
# 1. 创建Vue组件文件
src/components/tools/development/JsonFormatter.vue

# 2. 创建组合式函数（如需要）
src/composables/useJsonFormatter.js

# 3. 更新工具注册
src/data/tools.js

# 4. 添加国际化文本
src/locales/zh-CN.json
src/locales/en-US.json

# 5. 编写测试
tests/components/JsonFormatter.spec.js
```

## 总结

通过统一采用 Vue 3 组件化开发，ToolHub 项目将获得：

- **技术一致性**: 统一的开发技术栈
- **开发效率**: 组件复用和组合式函数
- **维护性**: 更好的代码组织和模块化
- **性能**: Vue 3 的优化和懒加载
- **国际化**: 内置的多语言支持
- **类型安全**: TypeScript 支持
- **测试性**: 更好的测试覆盖

所有新工具都应该按照本指南进行开发，现有HTML工具将逐步迁移到Vue组件。