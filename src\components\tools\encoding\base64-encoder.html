<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Base64编解码器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 手绘素描风格 */
        body {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            font-family: 'Comic Sans MS', cursive, sans-serif;
        }
        
        .sketch-border {
            border: 2px solid #d97706;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            position: relative;
        }
        
        .sketch-border::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 1px dashed #f59e0b;
            border-radius: 14px;
            pointer-events: none;
        }
        
        .btn-sketch {
            background: linear-gradient(145deg, #f59e0b, #d97706);
            border: 2px solid #b45309;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-sketch:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(245, 158, 11, 0.3);
        }
        
        .btn-sketch:active {
            transform: translateY(0);
        }
        
        .input-sketch {
            border: 2px solid #fbbf24;
            border-radius: 8px;
            padding: 12px;
            background: white;
            transition: border-color 0.3s ease;
        }
        
        .input-sketch:focus {
            outline: none;
            border-color: #f59e0b;
            box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
        }
        
        .result-area {
            background: linear-gradient(145deg, #fffbeb, #fef3c7);
            border: 2px solid #fbbf24;
            border-radius: 12px;
            min-height: 200px;
        }
        
        .error-text {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        .success-text {
            color: #059669;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        /* 装饰元素 */
        .doodle-star {
            position: absolute;
            color: #f59e0b;
            font-size: 20px;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        
        .encode-decode-arrow {
            stroke: #d97706;
            stroke-width: 3;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .tab-active {
            background: linear-gradient(145deg, #f59e0b, #d97706);
            color: white;
        }
        
        .tab-inactive {
            background: #fef3c7;
            color: #92400e;
            border: 2px solid #fbbf24;
        }
    </style>
</head>
<body class="min-h-screen p-6">
    <div class="max-w-6xl mx-auto">
        <!-- 标题区域 -->
        <div class="text-center mb-8 relative">
            <div class="doodle-star" style="top: 10px; left: 15%;">🔐</div>
            <div class="doodle-star" style="top: 15px; right: 20%; animation-delay: 0.5s;">🔓</div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">Base64编解码器</h1>
            <p class="text-lg text-gray-600">安全可靠的Base64编码解码在线工具</p>
            
            <!-- 编解码箭头 -->
            <svg class="absolute top-16 left-1/2 transform -translate-x-1/2" width="80" height="40" viewBox="0 0 80 40">
                <path class="encode-decode-arrow" d="M10 20 L35 20 M30 15 L35 20 L30 25" />
                <path class="encode-decode-arrow" d="M70 20 L45 20 M50 15 L45 20 L50 25" style="animation-delay: 1s;" />
            </svg>
        </div>
        
        <!-- 选项卡 -->
        <div class="flex justify-center mb-6">
            <div class="flex bg-white rounded-lg p-1 sketch-border">
                <button id="encodeTab" class="tab-active px-6 py-3 rounded-md font-semibold transition-all">
                    🔐 编码
                </button>
                <button id="decodeTab" class="tab-inactive px-6 py-3 rounded-md font-semibold transition-all">
                    🔓 解码
                </button>
            </div>
        </div>
        
        <!-- 编码模式 -->
        <div id="encodeMode" class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 输入区域 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">📝</span>
                    原始文本
                </h2>
                
                <textarea 
                    id="encodeInput" 
                    class="input-sketch w-full h-64 resize-none font-mono text-sm"
                    placeholder="请输入要编码的文本...\n\n支持中文、英文、特殊字符等\n例如：Hello World! 你好世界！"
                ></textarea>
                
                <!-- 操作按钮 -->
                <div class="flex flex-wrap gap-3 mt-4">
                    <button id="encodeBtn" class="btn-sketch">
                        <span class="mr-1">🔐</span>
                        编码
                    </button>
                    <button id="clearEncodeBtn" class="btn-sketch bg-gradient-to-r from-gray-500 to-gray-600 border-gray-700">
                        <span class="mr-1">🗑️</span>
                        清空
                    </button>
                </div>
            </div>
            
            <!-- 输出区域 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">📋</span>
                    Base64编码结果
                </h2>
                
                <div class="result-area p-4 relative">
                    <pre id="encodeOutput" class="font-mono text-sm whitespace-pre-wrap break-words h-64 overflow-auto"></pre>
                    
                    <!-- 复制按钮 -->
                    <button id="copyEncodeBtn" class="absolute top-2 right-2 bg-amber-500 hover:bg-amber-600 text-white px-3 py-1 rounded text-xs transition-colors">
                        📋 复制
                    </button>
                </div>
                
                <!-- 状态信息 -->
                <div id="encodeStatus" class="mt-4 text-sm"></div>
            </div>
        </div>
        
        <!-- 解码模式 -->
        <div id="decodeMode" class="grid grid-cols-1 lg:grid-cols-2 gap-8 hidden">
            <!-- 输入区域 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">🔐</span>
                    Base64编码文本
                </h2>
                
                <textarea 
                    id="decodeInput" 
                    class="input-sketch w-full h-64 resize-none font-mono text-sm"
                    placeholder="请输入Base64编码的文本...\n\n例如：SGVsbG8gV29ybGQh"
                ></textarea>
                
                <!-- 操作按钮 -->
                <div class="flex flex-wrap gap-3 mt-4">
                    <button id="decodeBtn" class="btn-sketch">
                        <span class="mr-1">🔓</span>
                        解码
                    </button>
                    <button id="clearDecodeBtn" class="btn-sketch bg-gradient-to-r from-gray-500 to-gray-600 border-gray-700">
                        <span class="mr-1">🗑️</span>
                        清空
                    </button>
                </div>
            </div>
            
            <!-- 输出区域 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">📋</span>
                    解码结果
                </h2>
                
                <div class="result-area p-4 relative">
                    <pre id="decodeOutput" class="font-mono text-sm whitespace-pre-wrap break-words h-64 overflow-auto"></pre>
                    
                    <!-- 复制按钮 -->
                    <button id="copyDecodeBtn" class="absolute top-2 right-2 bg-amber-500 hover:bg-amber-600 text-white px-3 py-1 rounded text-xs transition-colors">
                        📋 复制
                    </button>
                </div>
                
                <!-- 状态信息 -->
                <div id="decodeStatus" class="mt-4 text-sm"></div>
            </div>
        </div>
        
        <!-- 功能说明 -->
        <div class="mt-8 sketch-border bg-white p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                <span class="mr-2">💡</span>
                Base64编码说明
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">🔐 编码功能</h4>
                    <p>将任意文本转换为Base64编码格式，常用于数据传输和存储</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">🔓 解码功能</h4>
                    <p>将Base64编码的文本还原为原始内容</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">🌍 多语言支持</h4>
                    <p>支持中文、英文、特殊字符等各种文本编码</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">🔒 安全可靠</h4>
                    <p>本地处理，不上传数据，保护您的隐私安全</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        class Base64Converter {
            constructor() {
                this.initElements();
                this.bindEvents();
                this.loadExample();
            }
            
            initElements() {
                // 选项卡
                this.encodeTab = document.getElementById('encodeTab');
                this.decodeTab = document.getElementById('decodeTab');
                this.encodeMode = document.getElementById('encodeMode');
                this.decodeMode = document.getElementById('decodeMode');
                
                // 编码相关
                this.encodeInput = document.getElementById('encodeInput');
                this.encodeOutput = document.getElementById('encodeOutput');
                this.encodeStatus = document.getElementById('encodeStatus');
                this.encodeBtn = document.getElementById('encodeBtn');
                this.clearEncodeBtn = document.getElementById('clearEncodeBtn');
                this.copyEncodeBtn = document.getElementById('copyEncodeBtn');
                
                // 解码相关
                this.decodeInput = document.getElementById('decodeInput');
                this.decodeOutput = document.getElementById('decodeOutput');
                this.decodeStatus = document.getElementById('decodeStatus');
                this.decodeBtn = document.getElementById('decodeBtn');
                this.clearDecodeBtn = document.getElementById('clearDecodeBtn');
                this.copyDecodeBtn = document.getElementById('copyDecodeBtn');
            }
            
            bindEvents() {
                // 选项卡切换
                this.encodeTab.addEventListener('click', () => this.switchTab('encode'));
                this.decodeTab.addEventListener('click', () => this.switchTab('decode'));
                
                // 编码功能
                this.encodeBtn.addEventListener('click', () => this.encodeText());
                this.clearEncodeBtn.addEventListener('click', () => this.clearEncode());
                this.copyEncodeBtn.addEventListener('click', () => this.copyResult('encode'));
                
                // 解码功能
                this.decodeBtn.addEventListener('click', () => this.decodeText());
                this.clearDecodeBtn.addEventListener('click', () => this.clearDecode());
                this.copyDecodeBtn.addEventListener('click', () => this.copyResult('decode'));
                
                // 实时编解码
                this.encodeInput.addEventListener('input', () => {
                    this.debounce(() => this.encodeText(true), 300)();
                });
                
                this.decodeInput.addEventListener('input', () => {
                    this.debounce(() => this.decodeText(true), 300)();
                });
            }
            
            loadExample() {
                this.encodeInput.value = 'Hello World! 你好世界！\n这是一个Base64编码示例。';
                this.encodeText();
            }
            
            switchTab(mode) {
                if (mode === 'encode') {
                    this.encodeTab.className = 'tab-active px-6 py-3 rounded-md font-semibold transition-all';
                    this.decodeTab.className = 'tab-inactive px-6 py-3 rounded-md font-semibold transition-all';
                    this.encodeMode.classList.remove('hidden');
                    this.decodeMode.classList.add('hidden');
                } else {
                    this.decodeTab.className = 'tab-active px-6 py-3 rounded-md font-semibold transition-all';
                    this.encodeTab.className = 'tab-inactive px-6 py-3 rounded-md font-semibold transition-all';
                    this.decodeMode.classList.remove('hidden');
                    this.encodeMode.classList.add('hidden');
                }
            }
            
            encodeText(silent = false) {
                try {
                    const input = this.encodeInput.value;
                    if (!input) {
                        if (!silent) this.showStatus('请输入要编码的文本', 'error', 'encode');
                        this.encodeOutput.textContent = '';
                        return;
                    }
                    
                    // 使用btoa进行Base64编码，先转换为UTF-8
                    const encoded = btoa(unescape(encodeURIComponent(input)));
                    this.encodeOutput.textContent = encoded;
                    
                    if (!silent) {
                        const stats = `编码成功！原文本: ${input.length} 字符，编码后: ${encoded.length} 字符`;
                        this.showStatus(stats, 'success', 'encode');
                    }
                    
                } catch (error) {
                    this.showStatus(`编码失败: ${error.message}`, 'error', 'encode');
                    this.encodeOutput.textContent = '';
                }
            }
            
            decodeText(silent = false) {
                try {
                    const input = this.decodeInput.value.trim();
                    if (!input) {
                        if (!silent) this.showStatus('请输入Base64编码的文本', 'error', 'decode');
                        this.decodeOutput.textContent = '';
                        return;
                    }
                    
                    // 验证Base64格式
                    if (!/^[A-Za-z0-9+/]*={0,2}$/.test(input)) {
                        throw new Error('无效的Base64格式');
                    }
                    
                    // 使用atob进行Base64解码，然后转换为UTF-8
                    const decoded = decodeURIComponent(escape(atob(input)));
                    this.decodeOutput.textContent = decoded;
                    
                    if (!silent) {
                        const stats = `解码成功！编码文本: ${input.length} 字符，解码后: ${decoded.length} 字符`;
                        this.showStatus(stats, 'success', 'decode');
                    }
                    
                } catch (error) {
                    this.showStatus(`解码失败: ${error.message}`, 'error', 'decode');
                    this.decodeOutput.textContent = '';
                }
            }
            
            clearEncode() {
                this.encodeInput.value = '';
                this.encodeOutput.textContent = '';
                this.encodeStatus.innerHTML = '';
                this.showStatus('已清空编码内容', 'success', 'encode');
            }
            
            clearDecode() {
                this.decodeInput.value = '';
                this.decodeOutput.textContent = '';
                this.decodeStatus.innerHTML = '';
                this.showStatus('已清空解码内容', 'success', 'decode');
            }
            
            async copyResult(mode) {
                const output = mode === 'encode' ? this.encodeOutput : this.decodeOutput;
                const result = output.textContent;
                
                if (!result) {
                    this.showStatus('没有可复制的内容', 'error', mode);
                    return;
                }
                
                try {
                    await navigator.clipboard.writeText(result);
                    this.showStatus('已复制到剪贴板！', 'success', mode);
                    
                    // 按钮反馈
                    const copyBtn = mode === 'encode' ? this.copyEncodeBtn : this.copyDecodeBtn;
                    const originalText = copyBtn.innerHTML;
                    copyBtn.innerHTML = '✅ 已复制';
                    setTimeout(() => {
                        copyBtn.innerHTML = originalText;
                    }, 2000);
                    
                } catch (error) {
                    this.showStatus('复制失败，请手动复制', 'error', mode);
                }
            }
            
            showStatus(message, type, mode) {
                const className = type === 'error' ? 'error-text' : 'success-text';
                const statusElement = mode === 'encode' ? this.encodeStatus : this.decodeStatus;
                statusElement.innerHTML = `<div class="${className}">${message}</div>`;
                
                // 3秒后自动清除状态
                setTimeout(() => {
                    statusElement.innerHTML = '';
                }, 3000);
            }
            
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new Base64Converter();
        });
    </script>
</body>
</html>