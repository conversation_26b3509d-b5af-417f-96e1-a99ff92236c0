<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON格式化器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 手绘素描风格 */
        body {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            font-family: 'Comic Sans MS', cursive, sans-serif;
        }
        
        .sketch-border {
            border: 2px solid #64748b;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            position: relative;
        }
        
        .sketch-border::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 1px dashed #94a3b8;
            border-radius: 14px;
            pointer-events: none;
        }
        
        .btn-sketch {
            background: linear-gradient(145deg, #3b82f6, #2563eb);
            border: 2px solid #1e40af;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-sketch:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(59, 130, 246, 0.3);
        }
        
        .btn-sketch:active {
            transform: translateY(0);
        }
        
        .input-sketch {
            border: 2px solid #cbd5e1;
            border-radius: 8px;
            padding: 12px;
            background: white;
            transition: border-color 0.3s ease;
        }
        
        .input-sketch:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .result-area {
            background: linear-gradient(145deg, #f8fafc, #f1f5f9);
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            min-height: 200px;
        }
        
        .error-text {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        .success-text {
            color: #059669;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        /* 装饰元素 */
        .doodle-star {
            position: absolute;
            color: #fbbf24;
            font-size: 20px;
            animation: twinkle 2s infinite;
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }
        
        .hand-drawn-arrow {
            stroke: #64748b;
            stroke-width: 2;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
        }
    </style>
</head>
<body class="min-h-screen p-6">
    <div class="max-w-6xl mx-auto">
        <!-- 标题区域 -->
        <div class="text-center mb-8 relative">
            <div class="doodle-star" style="top: 10px; left: 20%;">★</div>
            <div class="doodle-star" style="top: 15px; right: 25%; animation-delay: 0.5s;">✨</div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">JSON格式化器</h1>
            <p class="text-lg text-gray-600">美化、压缩、验证JSON数据的在线工具</p>
            
            <!-- 手绘箭头 -->
            <svg class="absolute top-16 left-1/2 transform -translate-x-1/2" width="60" height="30" viewBox="0 0 60 30">
                <path class="hand-drawn-arrow" d="M10 15 Q30 5 50 15 M45 10 L50 15 L45 20" />
            </svg>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 输入区域 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">📝</span>
                    输入JSON数据
                </h2>
                
                <textarea 
                    id="jsonInput" 
                    class="input-sketch w-full h-64 resize-none font-mono text-sm"
                    placeholder="请输入JSON数据..."
                ></textarea>
                
                <!-- 操作按钮 -->
                <div class="flex flex-wrap gap-3 mt-4">
                    <button id="formatBtn" class="btn-sketch">
                        <span class="mr-1">✨</span>
                        格式化
                    </button>
                    <button id="compressBtn" class="btn-sketch bg-gradient-to-r from-emerald-500 to-emerald-600 border-emerald-700">
                        <span class="mr-1">📦</span>
                        压缩
                    </button>
                    <button id="validateBtn" class="btn-sketch bg-gradient-to-r from-amber-500 to-amber-600 border-amber-700">
                        <span class="mr-1">✅</span>
                        验证
                    </button>
                    <button id="clearBtn" class="btn-sketch bg-gradient-to-r from-gray-500 to-gray-600 border-gray-700">
                        <span class="mr-1">🗑️</span>
                        清空
                    </button>
                </div>
            </div>
            
            <!-- 输出区域 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">📋</span>
                    格式化结果
                </h2>
                
                <div class="result-area p-4 relative">
                    <pre id="jsonOutput" class="font-mono text-sm whitespace-pre-wrap break-words h-64 overflow-auto"></pre>
                    
                    <!-- 复制按钮 -->
                    <button id="copyBtn" class="absolute top-2 right-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs transition-colors">
                        📋 复制
                    </button>
                </div>
                
                <!-- 状态信息 -->
                <div id="statusInfo" class="mt-4 text-sm"></div>
            </div>
        </div>
        
        <!-- 功能说明 -->
        <div class="mt-8 sketch-border bg-white p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                <span class="mr-2">💡</span>
                使用说明
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">✨ 格式化功能</h4>
                    <p>将压缩的JSON数据格式化为易读的缩进格式</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">📦 压缩功能</h4>
                    <p>移除JSON中的空格和换行，减小数据体积</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">✅ 验证功能</h4>
                    <p>检查JSON语法是否正确，显示错误位置</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">📋 复制功能</h4>
                    <p>一键复制格式化后的结果到剪贴板</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        class JSONFormatter {
            constructor() {
                this.initElements();
                this.bindEvents();
                this.loadExample();
            }
            
            initElements() {
                this.jsonInput = document.getElementById('jsonInput');
                this.jsonOutput = document.getElementById('jsonOutput');
                this.statusInfo = document.getElementById('statusInfo');
                this.formatBtn = document.getElementById('formatBtn');
                this.compressBtn = document.getElementById('compressBtn');
                this.validateBtn = document.getElementById('validateBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.copyBtn = document.getElementById('copyBtn');
            }
            
            bindEvents() {
                this.formatBtn.addEventListener('click', () => this.formatJSON());
                this.compressBtn.addEventListener('click', () => this.compressJSON());
                this.validateBtn.addEventListener('click', () => this.validateJSON());
                this.clearBtn.addEventListener('click', () => this.clearAll());
                this.copyBtn.addEventListener('click', () => this.copyResult());
                
                // 实时验证
                this.jsonInput.addEventListener('input', () => {
                    this.debounce(() => this.validateJSON(true), 500)();
                });
            }
            
            loadExample() {
                const example = {
                    "name": "张三",
                    "age": 25,
                    "city": "北京",
                    "hobbies": ["读书", "旅行", "编程"],
                    "address": {
                        "street": "中关村大街",
                        "number": 123
                    }
                };
                this.jsonInput.value = JSON.stringify(example);
                this.formatJSON();
            }
            
            formatJSON() {
                try {
                    const input = this.jsonInput.value.trim();
                    if (!input) {
                        this.showStatus('请输入JSON数据', 'error');
                        return;
                    }
                    
                    const parsed = JSON.parse(input);
                    const formatted = JSON.stringify(parsed, null, 2);
                    this.jsonOutput.textContent = formatted;
                    this.showStatus('JSON格式化成功！', 'success');
                    
                } catch (error) {
                    this.showStatus(`JSON格式错误: ${error.message}`, 'error');
                    this.jsonOutput.textContent = '';
                }
            }
            
            compressJSON() {
                try {
                    const input = this.jsonInput.value.trim();
                    if (!input) {
                        this.showStatus('请输入JSON数据', 'error');
                        return;
                    }
                    
                    const parsed = JSON.parse(input);
                    const compressed = JSON.stringify(parsed);
                    this.jsonOutput.textContent = compressed;
                    
                    const originalSize = input.length;
                    const compressedSize = compressed.length;
                    const savings = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);
                    
                    this.showStatus(`JSON压缩成功！节省了 ${savings}% 的空间`, 'success');
                    
                } catch (error) {
                    this.showStatus(`JSON格式错误: ${error.message}`, 'error');
                    this.jsonOutput.textContent = '';
                }
            }
            
            validateJSON(silent = false) {
                try {
                    const input = this.jsonInput.value.trim();
                    if (!input) {
                        if (!silent) this.showStatus('请输入JSON数据', 'error');
                        return false;
                    }
                    
                    JSON.parse(input);
                    if (!silent) {
                        this.showStatus('JSON格式正确！', 'success');
                        this.jsonOutput.textContent = '✅ JSON语法验证通过';
                    }
                    return true;
                    
                } catch (error) {
                    if (!silent) {
                        this.showStatus(`JSON格式错误: ${error.message}`, 'error');
                        this.jsonOutput.textContent = `❌ 错误详情:\n${error.message}`;
                    }
                    return false;
                }
            }
            
            clearAll() {
                this.jsonInput.value = '';
                this.jsonOutput.textContent = '';
                this.statusInfo.innerHTML = '';
                this.showStatus('已清空所有内容', 'success');
            }
            
            async copyResult() {
                const result = this.jsonOutput.textContent;
                if (!result) {
                    this.showStatus('没有可复制的内容', 'error');
                    return;
                }
                
                try {
                    await navigator.clipboard.writeText(result);
                    this.showStatus('已复制到剪贴板！', 'success');
                    
                    // 按钮反馈
                    const originalText = this.copyBtn.innerHTML;
                    this.copyBtn.innerHTML = '✅ 已复制';
                    setTimeout(() => {
                        this.copyBtn.innerHTML = originalText;
                    }, 2000);
                    
                } catch (error) {
                    this.showStatus('复制失败，请手动复制', 'error');
                }
            }
            
            showStatus(message, type) {
                const className = type === 'error' ? 'error-text' : 'success-text';
                this.statusInfo.innerHTML = `<div class="${className}">${message}</div>`;
                
                // 3秒后自动清除状态
                setTimeout(() => {
                    this.statusInfo.innerHTML = '';
                }, 3000);
            }
            
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new JSONFormatter();
        });
    </script>
</body>
</html>