<template>
  <aside class="sidebar">
    <nav>
      <ul>
        <li v-for="category in categories" :key="category.id">
          <a 
            href="#" 
            :class="{ active: selectedCategory === category.id }"
            :data-category="category.id"
            @click.prevent="handleCategoryClick(category.id)"
          >
            {{ category.name }}
          </a>
        </li>
      </ul>
    </nav>
  </aside>
</template>

<script setup>
const emit = defineEmits(['category-change'])

defineProps({
  categories: {
    type: Array,
    required: true
  },
  selectedCategory: {
    type: String,
    required: true
  }
})

const handleCategoryClick = (categoryId) => {
  emit('category-change', categoryId)
}
</script>

<style scoped>
.sidebar {
  width: 100%;
  height: 100%;
  font-family: 'Comic Sans MS', 'Chalkduster', 'Marker Felt', cursive, sans-serif;
}

.sidebar nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar nav ul li {
  margin-bottom: 1rem;
  position: relative;
}

/* 手绘标签分隔符效果 */
.sidebar nav ul li::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%) rotate(-2deg);
  width: 6px;
  height: 25px;
  background: linear-gradient(45deg, #B7DFBA, #7B9E89);
  border-radius: 3px;
  box-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.sidebar nav ul li a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.7rem 0.6rem;
  text-decoration: none;
  color: #4A4038;
  border-radius: 12px;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 0.85rem;
  position: relative;
  background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(247,244,233,0.8) 100%);
  border: 2px dashed transparent;
  transform: rotate(-0.5deg);
  box-shadow: 2px 2px 4px rgba(123, 158, 137, 0.1);
  width: auto;
  min-width: 120px;
  max-width: 160px;
}

.sidebar nav ul li a:hover {
  background: linear-gradient(135deg, #E8E2D8 0%, #D4B896 100%);
  color: #4A4038;
  transform: rotate(0.5deg) scale(1.02);
  border-color: #B7DFBA;
  box-shadow: 3px 3px 6px rgba(123, 158, 137, 0.2);
}

.sidebar nav ul li a.active {
  background: linear-gradient(135deg, #B7DFBA 0%, #7B9E89 100%);
  color: white;
  border-color: #7B9E89;
  transform: rotate(1deg) scale(1.05);
  box-shadow: 4px 4px 8px rgba(123, 158, 137, 0.3);
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.sidebar nav ul li a.active:hover {
  background: linear-gradient(135deg, #7B9E89 0%, #6B8E5A 100%);
  transform: rotate(-0.5deg) scale(1.05);
}

/* 添加手绘图标效果 */
.sidebar nav ul li a::after {
  content: '📁';
  flex-shrink: 0;
  font-size: 0.9rem;
  opacity: 0.6;
  transition: all 0.3s ease;
  margin-left: 0.3rem;
}

.sidebar nav ul li a:hover::after {
  opacity: 1;
  transform: rotate(10deg);
}

.sidebar nav ul li a.active::after {
  content: '📂';
  opacity: 1;
  transform: rotate(-5deg);
}

/* 添加装饰性涂鸦效果 */
.sidebar nav ul li:nth-child(odd) a {
  transform: rotate(0.5deg);
}

.sidebar nav ul li:nth-child(even) a {
  transform: rotate(-0.5deg);
}

.sidebar nav ul li:nth-child(3n) a::before {
  content: '⭐';
  position: absolute;
  left: -8px;
  top: -5px;
  font-size: 0.8rem;
  opacity: 0.4;
  animation: twinkle 3s ease-in-out infinite;
}

@keyframes twinkle {
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.2); }
}
</style>
