<template>
  <svg 
    class="icon-svg" 
    viewBox="0 0 100 100" 
    xmlns="http://www.w3.org/2000/svg"
  >
    <!-- 背景圆圈 -->
    <circle 
      cx="50" 
      cy="50" 
      r="45" 
      fill="#fef3c7" 
      stroke="#f59e0b" 
      stroke-width="2"
      class="bg-circle"
    />
    
    <!-- 文本框背景 -->
    <rect 
      x="20" 
      y="30" 
      width="60" 
      height="40" 
      rx="5" 
      fill="#d97706" 
      stroke="#92400e" 
      stroke-width="2"
      class="text-container"
    />
    
    <!-- 分割线 -->
    <line 
      x1="20" 
      y1="50" 
      x2="80" 
      y2="50" 
      stroke="#92400e" 
      stroke-width="2" 
      stroke-dasharray="5,3"
      class="divider"
    />
    
    <!-- 大写字母 -->
    <g class="uppercase-section">
      <text x="35" y="45" text-anchor="middle" font-family="serif" font-size="12" fill="#fef3c7" font-weight="bold" class="letter upper-a">A</text>
      <text x="50" y="45" text-anchor="middle" font-family="serif" font-size="12" fill="#fef3c7" font-weight="bold" class="letter upper-b">B</text>
      <text x="65" y="45" text-anchor="middle" font-family="serif" font-size="12" fill="#fef3c7" font-weight="bold" class="letter upper-c">C</text>
    </g>
    
    <!-- 小写字母 -->
    <g class="lowercase-section">
      <text x="35" y="63" text-anchor="middle" font-family="serif" font-size="10" fill="#fef3c7" class="letter lower-a">a</text>
      <text x="50" y="63" text-anchor="middle" font-family="serif" font-size="10" fill="#fef3c7" class="letter lower-b">b</text>
      <text x="65" y="63" text-anchor="middle" font-family="serif" font-size="10" fill="#fef3c7" class="letter lower-c">c</text>
    </g>
    
    <!-- 转换箭头 -->
    <g class="conversion-arrows">
      <!-- 上下箭头 -->
      <path 
        d="M25 47 L30 42 M25 47 L20 42 M25 47 L25 37" 
        stroke="#fbbf24" 
        stroke-width="2" 
        fill="none" 
        stroke-linecap="round"
        class="arrow-up"
      />
      
      <path 
        d="M25 53 L30 58 M25 53 L20 58 M25 53 L25 63" 
        stroke="#fbbf24" 
        stroke-width="2" 
        fill="none" 
        stroke-linecap="round"
        class="arrow-down"
      />
    </g>
    
    <!-- 功能标签 -->
    <text x="50" y="85" text-anchor="middle" font-family="monospace" font-size="8" fill="#92400e" font-weight="bold" class="function-label">CASE</text>
    
    <!-- 装饰性字母 -->
    <g class="decorative-letters">
      <text x="15" y="20" font-family="serif" font-size="8" fill="#f59e0b" class="deco-letter dl1">Aa</text>
      <text x="80" y="20" font-family="serif" font-size="8" fill="#f59e0b" class="deco-letter dl2">Bb</text>
      <text x="10" y="80" font-family="serif" font-size="8" fill="#f59e0b" class="deco-letter dl3">Cc</text>
      <text x="85" y="80" font-family="serif" font-size="8" fill="#f59e0b" class="deco-letter dl4">Dd</text>
    </g>
    
    <!-- 变换效果线条 -->
    <g class="transform-lines">
      <line x1="15" y1="25" x2="25" y2="35" stroke="#fbbf24" stroke-width="1" opacity="0.6" class="transform-line tl1" />
      <line x1="85" y1="25" x2="75" y2="35" stroke="#fbbf24" stroke-width="1" opacity="0.6" class="transform-line tl2" />
      <line x1="15" y1="75" x2="25" y2="65" stroke="#fbbf24" stroke-width="1" opacity="0.6" class="transform-line tl3" />
      <line x1="85" y1="75" x2="75" y2="65" stroke="#fbbf24" stroke-width="1" opacity="0.6" class="transform-line tl4" />
    </g>
  </svg>
</template>

<style scoped>
.icon-svg {
  width: 60px;
  height: 60px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.icon-svg:hover {
  transform: scale(1.1) rotate(-3deg);
}

.icon-svg:hover .bg-circle {
  fill: #fde68a;
  stroke: #d97706;
  animation: pulse 1s infinite;
}

.icon-svg:hover .text-container {
  animation: containerShake 0.8s ease-in-out infinite;
}

.icon-svg:hover .divider {
  animation: dividerPulse 1.5s ease-in-out infinite;
}

.icon-svg:hover .letter {
  animation: letterTransform 2s ease-in-out infinite;
}

.icon-svg:hover .upper-a {
  animation-delay: 0s;
}

.icon-svg:hover .upper-b {
  animation-delay: 0.2s;
}

.icon-svg:hover .upper-c {
  animation-delay: 0.4s;
}

.icon-svg:hover .lower-a {
  animation-delay: 0.6s;
}

.icon-svg:hover .lower-b {
  animation-delay: 0.8s;
}

.icon-svg:hover .lower-c {
  animation-delay: 1s;
}

.icon-svg:hover .arrow-up {
  animation: arrowBounceUp 1.2s ease-in-out infinite;
}

.icon-svg:hover .arrow-down {
  animation: arrowBounceDown 1.2s ease-in-out infinite;
}

.icon-svg:hover .function-label {
  animation: labelPulse 1s ease-in-out infinite;
}

.icon-svg:hover .deco-letter {
  animation: decoFloat 3s ease-in-out infinite;
}

.icon-svg:hover .dl1 {
  animation-delay: 0s;
}

.icon-svg:hover .dl2 {
  animation-delay: 0.75s;
}

.icon-svg:hover .dl3 {
  animation-delay: 1.5s;
}

.icon-svg:hover .dl4 {
  animation-delay: 2.25s;
}

.icon-svg:hover .transform-line {
  animation: lineGlow 2s ease-in-out infinite;
}

.icon-svg:hover .tl1 {
  animation-delay: 0.2s;
}

.icon-svg:hover .tl2 {
  animation-delay: 0.4s;
}

.icon-svg:hover .tl3 {
  animation-delay: 0.6s;
}

.icon-svg:hover .tl4 {
  animation-delay: 0.8s;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes containerShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-1px); }
  75% { transform: translateX(1px); }
}

@keyframes dividerPulse {
  0%, 100% { stroke-dasharray: 5,3; }
  50% { stroke-dasharray: 8,2; }
}

@keyframes letterTransform {
  0%, 100% { transform: scale(1) rotateY(0deg); fill: #fef3c7; }
  25% { transform: scale(1.2) rotateY(90deg); fill: #fbbf24; }
  50% { transform: scale(1) rotateY(180deg); fill: #f59e0b; }
  75% { transform: scale(1.2) rotateY(270deg); fill: #fbbf24; }
}

@keyframes arrowBounceUp {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

@keyframes arrowBounceDown {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(3px); }
}

@keyframes labelPulse {
  0%, 100% { font-size: 8px; }
  50% { font-size: 9px; }
}

@keyframes decoFloat {
  0%, 100% { transform: translateY(0) scale(1); }
  33% { transform: translateY(-5px) scale(1.1); }
  66% { transform: translateY(2px) scale(0.9); }
}

@keyframes lineGlow {
  0%, 100% { opacity: 0.6; stroke-width: 1; }
  50% { opacity: 1; stroke-width: 2; }
}
</style>