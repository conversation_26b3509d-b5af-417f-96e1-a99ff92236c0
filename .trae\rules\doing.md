`d:\code\nodeWork\ToolHub\docs\开发计划.md` `d:\code\nodeWork\ToolHub\docs\tool_implementation_plan.md`，`d:/code/nodeWork/ToolHub/docs/progress.md` 阅读现有的文档，
阅读 `https://33tool.com/` 这个网站，对比我现有网站的工具。分析一下我现有网站的工具，对比一下33tool.com的工具，看看我现有网站的工具是否缺少33tool.com的工具，看看33tool.com的工具是否有我现有网站的工具没有的。

帮我实现10个目前没有的工具，每个工具都是一个Vue单文件组件(.vue)，使用Vue 3 Composition API + Tailwind CSS v4.1开发，Vue组件文件写在tools文件夹中 `d:\code\nodeWork\ToolHub\src\components\tools` 

参考文档中的目录结构和Vue开发规范，注意工具要按照菜单分类在tools下创建目录，使用ToolLayout和ResultDisplay等通用组件，可以使用context7查看相关的API指导开发，完成后使用playwright mcp打开页面测试，

最后在主页添加这些个工具的导航卡片 `d:/code/nodeWork/ToolHub/docs/开发计划.md#L67-135`，注意工具页面样式要遵循现有的Vue组件样式，复用现有的组件和composable函数，图标也同样使用svg来画，参考 `d:/code/nodeWork/ToolHub/src/components/icon` 中的图标
完成工具和导航页面后，更新 `d:/code/nodeWork/ToolHub/docs/progress.md` 文件，把当前进度写入