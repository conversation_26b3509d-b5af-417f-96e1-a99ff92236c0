<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MD5加密工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <style>
        /* 手绘素描风格 */
        body {
            background: linear-gradient(135deg, #fce7f3 0%, #f3e8ff 100%);
            font-family: 'Comic Sans MS', cursive, sans-serif;
        }
        
        .sketch-border {
            border: 2px solid #a855f7;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            position: relative;
        }
        
        .sketch-border::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 1px dashed #c084fc;
            border-radius: 14px;
            pointer-events: none;
        }
        
        .btn-sketch {
            background: linear-gradient(145deg, #a855f7, #9333ea);
            border: 2px solid #7c3aed;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-sketch:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(168, 85, 247, 0.3);
        }
        
        .btn-sketch:active {
            transform: translateY(0);
        }
        
        .input-sketch {
            border: 2px solid #c084fc;
            border-radius: 8px;
            padding: 12px;
            background: white;
            transition: border-color 0.3s ease;
        }
        
        .input-sketch:focus {
            outline: none;
            border-color: #a855f7;
            box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
        }
        
        .result-area {
            background: linear-gradient(145deg, #faf5ff, #f3e8ff);
            border: 2px solid #c084fc;
            border-radius: 12px;
            min-height: 120px;
        }
        
        .hash-result {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
            background: #1f2937;
            color: #10b981;
            padding: 16px;
            border-radius: 8px;
            border: 2px solid #374151;
        }
        
        .error-text {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        .success-text {
            color: #059669;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        /* 装饰元素 */
        .doodle-star {
            position: absolute;
            color: #a855f7;
            font-size: 20px;
            animation: rotate 3s linear infinite;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .security-icon {
            stroke: #a855f7;
            stroke-width: 2;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { filter: drop-shadow(0 0 5px #a855f7); }
            to { filter: drop-shadow(0 0 15px #a855f7); }
        }
        
        .hash-type-btn {
            padding: 8px 16px;
            border: 2px solid #c084fc;
            border-radius: 6px;
            background: white;
            color: #7c3aed;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .hash-type-btn.active {
            background: linear-gradient(145deg, #a855f7, #9333ea);
            color: white;
            border-color: #7c3aed;
        }
        
        .hash-type-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(168, 85, 247, 0.2);
        }
    </style>
</head>
<body class="min-h-screen p-6">
    <div class="max-w-6xl mx-auto">
        <!-- 标题区域 -->
        <div class="text-center mb-8 relative">
            <div class="doodle-star" style="top: 10px; left: 15%;">🔐</div>
            <div class="doodle-star" style="top: 15px; right: 20%; animation-delay: 1s;">🛡️</div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">MD5加密工具</h1>
            <p class="text-lg text-gray-600">安全可靠的哈希加密在线工具</p>
            
            <!-- 安全图标 -->
            <svg class="absolute top-16 left-1/2 transform -translate-x-1/2" width="60" height="40" viewBox="0 0 60 40">
                <path class="security-icon" d="M30 5 L45 15 L45 30 Q45 35 30 35 Q15 35 15 30 L15 15 Z" />
                <circle class="security-icon" cx="30" cy="22" r="4" style="animation-delay: 0.5s;" />
            </svg>
        </div>
        
        <!-- 哈希类型选择 -->
        <div class="flex justify-center mb-6">
            <div class="flex flex-wrap gap-3 bg-white rounded-lg p-3 sketch-border">
                <button id="md5Btn" class="hash-type-btn active" data-type="md5">MD5</button>
                <button id="sha1Btn" class="hash-type-btn" data-type="sha1">SHA1</button>
                <button id="sha256Btn" class="hash-type-btn" data-type="sha256">SHA256</button>
                <button id="sha512Btn" class="hash-type-btn" data-type="sha512">SHA512</button>
            </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 输入区域 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">📝</span>
                    输入文本
                </h2>
                
                <textarea 
                    id="textInput" 
                    class="input-sketch w-full h-48 resize-none text-sm"
                    placeholder="请输入要加密的文本...\n\n支持任意长度的文本\n例如：Hello World!"
                ></textarea>
                
                <!-- 文件上传 -->
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">或上传文件：</label>
                    <input 
                        type="file" 
                        id="fileInput" 
                        class="input-sketch w-full text-sm"
                        accept=".txt,.json,.xml,.csv"
                    >
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex flex-wrap gap-3 mt-4">
                    <button id="hashBtn" class="btn-sketch">
                        <span class="mr-1">🔐</span>
                        生成哈希
                    </button>
                    <button id="clearBtn" class="btn-sketch bg-gradient-to-r from-gray-500 to-gray-600 border-gray-700">
                        <span class="mr-1">🗑️</span>
                        清空
                    </button>
                </div>
            </div>
            
            <!-- 输出区域 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">🔐</span>
                    <span id="hashTypeLabel">MD5</span> 哈希值
                </h2>
                
                <div class="result-area p-4 relative">
                    <div id="hashOutput" class="hash-result min-h-[100px] flex items-center justify-center text-gray-500">
                        等待输入文本...
                    </div>
                    
                    <!-- 复制按钮 -->
                    <button id="copyBtn" class="absolute top-2 right-2 bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-xs transition-colors">
                        📋 复制
                    </button>
                </div>
                
                <!-- 哈希信息 -->
                <div id="hashInfo" class="mt-4 text-sm space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-600">哈希长度：</span>
                        <span id="hashLength" class="font-mono">-</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">输入长度：</span>
                        <span id="inputLength" class="font-mono">-</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">处理时间：</span>
                        <span id="processTime" class="font-mono">-</span>
                    </div>
                </div>
                
                <!-- 状态信息 -->
                <div id="statusInfo" class="mt-4 text-sm"></div>
            </div>
        </div>
        
        <!-- 功能说明 -->
        <div class="mt-8 sketch-border bg-white p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                <span class="mr-2">💡</span>
                哈希加密说明
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">🔐 MD5算法</h4>
                    <p>128位哈希值，常用于文件校验和数据完整性验证</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">🛡️ SHA系列</h4>
                    <p>更安全的哈希算法，SHA256和SHA512提供更高安全性</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">📁 文件支持</h4>
                    <p>支持上传文本文件进行哈希计算</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">🔒 本地处理</h4>
                    <p>所有计算在本地完成，保护您的数据安全</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        class HashGenerator {
            constructor() {
                this.currentHashType = 'md5';
                this.initElements();
                this.bindEvents();
                this.loadExample();
            }
            
            initElements() {
                this.textInput = document.getElementById('textInput');
                this.fileInput = document.getElementById('fileInput');
                this.hashOutput = document.getElementById('hashOutput');
                this.statusInfo = document.getElementById('statusInfo');
                this.hashBtn = document.getElementById('hashBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.copyBtn = document.getElementById('copyBtn');
                
                // 哈希类型按钮
                this.md5Btn = document.getElementById('md5Btn');
                this.sha1Btn = document.getElementById('sha1Btn');
                this.sha256Btn = document.getElementById('sha256Btn');
                this.sha512Btn = document.getElementById('sha512Btn');
                
                // 信息显示
                this.hashTypeLabel = document.getElementById('hashTypeLabel');
                this.hashLength = document.getElementById('hashLength');
                this.inputLength = document.getElementById('inputLength');
                this.processTime = document.getElementById('processTime');
            }
            
            bindEvents() {
                // 哈希类型切换
                [this.md5Btn, this.sha1Btn, this.sha256Btn, this.sha512Btn].forEach(btn => {
                    btn.addEventListener('click', (e) => this.switchHashType(e.target.dataset.type));
                });
                
                // 主要功能
                this.hashBtn.addEventListener('click', () => this.generateHash());
                this.clearBtn.addEventListener('click', () => this.clearAll());
                this.copyBtn.addEventListener('click', () => this.copyResult());
                
                // 文件上传
                this.fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
                
                // 实时哈希
                this.textInput.addEventListener('input', () => {
                    this.debounce(() => this.generateHash(true), 500)();
                });
            }
            
            loadExample() {
                this.textInput.value = 'Hello World! 这是一个MD5加密示例。';
                this.generateHash();
            }
            
            switchHashType(type) {
                this.currentHashType = type;
                
                // 更新按钮状态
                document.querySelectorAll('.hash-type-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector(`[data-type="${type}"]`).classList.add('active');
                
                // 更新标签
                this.hashTypeLabel.textContent = type.toUpperCase();
                
                // 重新生成哈希
                if (this.textInput.value) {
                    this.generateHash();
                }
            }
            
            generateHash(silent = false) {
                const startTime = performance.now();
                
                try {
                    const input = this.textInput.value;
                    if (!input) {
                        this.hashOutput.innerHTML = '<div class="text-gray-500 text-center">等待输入文本...</div>';
                        this.updateInfo('', '', '');
                        return;
                    }
                    
                    let hash;
                    switch (this.currentHashType) {
                        case 'md5':
                            hash = CryptoJS.MD5(input).toString();
                            break;
                        case 'sha1':
                            hash = CryptoJS.SHA1(input).toString();
                            break;
                        case 'sha256':
                            hash = CryptoJS.SHA256(input).toString();
                            break;
                        case 'sha512':
                            hash = CryptoJS.SHA512(input).toString();
                            break;
                        default:
                            throw new Error('不支持的哈希类型');
                    }
                    
                    const endTime = performance.now();
                    const processTime = (endTime - startTime).toFixed(2);
                    
                    this.hashOutput.textContent = hash;
                    this.updateInfo(hash.length, input.length, processTime);
                    
                    if (!silent) {
                        this.showStatus(`${this.currentHashType.toUpperCase()}哈希生成成功！`, 'success');
                    }
                    
                } catch (error) {
                    this.showStatus(`哈希生成失败: ${error.message}`, 'error');
                    this.hashOutput.innerHTML = '<div class="text-red-500 text-center">生成失败</div>';
                    this.updateInfo('', '', '');
                }
            }
            
            handleFileUpload(event) {
                const file = event.target.files[0];
                if (!file) return;
                
                // 检查文件大小（限制为10MB）
                if (file.size > 10 * 1024 * 1024) {
                    this.showStatus('文件大小不能超过10MB', 'error');
                    return;
                }
                
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.textInput.value = e.target.result;
                    this.generateHash();
                    this.showStatus(`文件 "${file.name}" 加载成功`, 'success');
                };
                
                reader.onerror = () => {
                    this.showStatus('文件读取失败', 'error');
                };
                
                reader.readAsText(file);
            }
            
            updateInfo(hashLength, inputLength, processTime) {
                this.hashLength.textContent = hashLength ? `${hashLength} 字符` : '-';
                this.inputLength.textContent = inputLength ? `${inputLength} 字符` : '-';
                this.processTime.textContent = processTime ? `${processTime} ms` : '-';
            }
            
            clearAll() {
                this.textInput.value = '';
                this.fileInput.value = '';
                this.hashOutput.innerHTML = '<div class="text-gray-500 text-center">等待输入文本...</div>';
                this.statusInfo.innerHTML = '';
                this.updateInfo('', '', '');
                this.showStatus('已清空所有内容', 'success');
            }
            
            async copyResult() {
                const result = this.hashOutput.textContent;
                if (!result || result.includes('等待输入') || result.includes('生成失败')) {
                    this.showStatus('没有可复制的哈希值', 'error');
                    return;
                }
                
                try {
                    await navigator.clipboard.writeText(result);
                    this.showStatus('哈希值已复制到剪贴板！', 'success');
                    
                    // 按钮反馈
                    const originalText = this.copyBtn.innerHTML;
                    this.copyBtn.innerHTML = '✅ 已复制';
                    setTimeout(() => {
                        this.copyBtn.innerHTML = originalText;
                    }, 2000);
                    
                } catch (error) {
                    this.showStatus('复制失败，请手动复制', 'error');
                }
            }
            
            showStatus(message, type) {
                const className = type === 'error' ? 'error-text' : 'success-text';
                this.statusInfo.innerHTML = `<div class="${className}">${message}</div>`;
                
                // 3秒后自动清除状态
                setTimeout(() => {
                    this.statusInfo.innerHTML = '';
                }, 3000);
            }
            
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new HashGenerator();
        });
    </script>
</body>
</html>