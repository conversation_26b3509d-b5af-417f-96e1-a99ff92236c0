<template>
  <svg 
    class="icon-svg" 
    viewBox="0 0 100 100" 
    xmlns="http://www.w3.org/2000/svg"
  >
    <!-- 背景圆圈 -->
    <circle 
      cx="50" 
      cy="50" 
      r="45" 
      fill="#fef3c7" 
      stroke="#f59e0b" 
      stroke-width="2"
      class="bg-circle"
    />
    
    <!-- 盾牌形状 -->
    <path 
      d="M50 15 L65 25 L65 45 Q65 60 50 75 Q35 60 35 45 L35 25 Z" 
      fill="#d97706" 
      stroke="#92400e" 
      stroke-width="2"
      class="shield"
    />
    
    <!-- MD5 文字 -->
    <text x="50" y="35" text-anchor="middle" font-family="monospace" font-size="8" fill="#fef3c7" font-weight="bold" class="md5-text">MD5</text>
    
    <!-- 哈希符号 # -->
    <g class="hash-symbol">
      <line x1="45" y1="45" x2="55" y2="45" stroke="#fef3c7" stroke-width="2" />
      <line x1="45" y1="52" x2="55" y2="52" stroke="#fef3c7" stroke-width="2" />
      <line x1="48" y1="42" x2="48" y2="55" stroke="#fef3c7" stroke-width="2" />
      <line x1="52" y1="42" x2="52" y2="55" stroke="#fef3c7" stroke-width="2" />
    </g>
    
    <!-- 数据流动效果 -->
    <circle cx="25" cy="30" r="2" fill="#f59e0b" class="data-dot dot1" />
    <circle cx="75" cy="35" r="2" fill="#f59e0b" class="data-dot dot2" />
    <circle cx="20" cy="50" r="2" fill="#f59e0b" class="data-dot dot3" />
    <circle cx="80" cy="55" r="2" fill="#f59e0b" class="data-dot dot4" />
    
    <!-- 输出哈希值表示 -->
    <rect x="25" y="80" width="50" height="8" rx="2" fill="#92400e" class="hash-output" />
    <rect x="27" y="82" width="8" height="4" rx="1" fill="#fbbf24" class="hash-block block1" />
    <rect x="37" y="82" width="6" height="4" rx="1" fill="#fbbf24" class="hash-block block2" />
    <rect x="45" y="82" width="10" height="4" rx="1" fill="#fbbf24" class="hash-block block3" />
    <rect x="57" y="82" width="7" height="4" rx="1" fill="#fbbf24" class="hash-block block4" />
    <rect x="66" y="82" width="7" height="4" rx="1" fill="#fbbf24" class="hash-block block5" />
  </svg>
</template>

<style scoped>
.icon-svg {
  width: 60px;
  height: 60px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.icon-svg:hover {
  transform: scale(1.1) rotate(2deg);
}

.icon-svg:hover .bg-circle {
  fill: #fde68a;
  stroke: #d97706;
  animation: pulse 1s infinite;
}

.icon-svg:hover .shield {
  animation: shieldGlow 1.2s ease-in-out infinite;
}

.icon-svg:hover .md5-text {
  animation: textPulse 0.8s ease-in-out infinite;
}

.icon-svg:hover .hash-symbol {
  animation: rotate 2s linear infinite;
}

.icon-svg:hover .data-dot {
  animation: orbit 2s linear infinite;
}

.icon-svg:hover .dot1 {
  animation-delay: 0s;
}

.icon-svg:hover .dot2 {
  animation-delay: 0.5s;
}

.icon-svg:hover .dot3 {
  animation-delay: 1s;
}

.icon-svg:hover .dot4 {
  animation-delay: 1.5s;
}

.icon-svg:hover .hash-output {
  animation: slideUp 0.6s ease-out;
}

.icon-svg:hover .hash-block {
  animation: blockFill 1.5s ease-in-out infinite;
}

.icon-svg:hover .block1 {
  animation-delay: 0.1s;
}

.icon-svg:hover .block2 {
  animation-delay: 0.2s;
}

.icon-svg:hover .block3 {
  animation-delay: 0.3s;
}

.icon-svg:hover .block4 {
  animation-delay: 0.4s;
}

.icon-svg:hover .block5 {
  animation-delay: 0.5s;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes shieldGlow {
  0%, 100% { filter: drop-shadow(0 0 5px #f59e0b); }
  50% { filter: drop-shadow(0 0 15px #fbbf24); }
}

@keyframes textPulse {
  0%, 100% { font-size: 8px; }
  50% { font-size: 9px; }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes orbit {
  0% { transform: translateX(0) translateY(0); }
  25% { transform: translateX(5px) translateY(-5px); }
  50% { transform: translateX(0) translateY(-10px); }
  75% { transform: translateX(-5px) translateY(-5px); }
  100% { transform: translateX(0) translateY(0); }
}

@keyframes slideUp {
  0% { transform: translateY(10px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes blockFill {
  0%, 100% { fill: #fbbf24; }
  50% { fill: #f59e0b; }
}
</style>