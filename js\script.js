document.addEventListener('DOMContentLoaded', () => {
    const themeToggle = document.getElementById('theme-toggle');
    const searchInput = document.getElementById('search');
    const toolGrid = document.querySelector('.tool-grid');
    const categoryLinks = document.querySelectorAll('.sidebar nav a');

    const tools = [
        { name: 'AI 作家', description: '生成高质量的文章和营销文案。', category: 'writing', tags: ['内容', 'SEO'] },
        { name: '图像生成器', description: '根据文本提示创建令人惊叹的视觉效果。', category: 'image', tags: ['艺术', '设计'] },
        { name: '视频摘要器', description: '立即从长视频中获取关键见解。', category: 'video', tags: ['生产力', '教育'] },
        { name: '代码助手', description: '您的 AI 结对程序员，可加快编码速度。', category: 'code', tags: ['开发', '生产力'] },
        { name: 'Logo 设计器', description: '为您的品牌设计专业的 Logo。', category: 'image', tags: ['品牌', '设计'] },
        { name: '画外音生成器', description: '以任何语言创建听起来自然的画外音。', category: 'video', tags: ['音频', '营销'] },
        { name: '语法检查器', description: '纠正语法并改善您的写作风格。', category: 'writing', tags: ['校对', '编辑'] },
        { name: 'API 测试器', description: '轻松测试和调试您的 API。', category: 'code', tags: ['开发', '测试'] },
    ];

    function renderTools(filter = 'all', searchTerm = '') {
        toolGrid.innerHTML = '';
        const filteredTools = tools.filter(tool => {
            const categoryMatch = filter === 'all' || tool.category === filter;
            const searchMatch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                tool.description.toLowerCase().includes(searchTerm.toLowerCase());
            return categoryMatch && searchMatch;
        });

        if (filteredTools.length === 0) {
            toolGrid.innerHTML = '<p>未找到任何工具。</p>';
            return;
        }

        filteredTools.forEach(tool => {
            const toolCard = `
                <div class="tool-card" data-category="${tool.category}">
                    <div class="card-icon"></div>
                    <h3 class="card-title">${tool.name}</h3>
                    <p class="card-description">${tool.description}</p>
                    <div class="card-tags">
                        ${tool.tags.map(tag => `<span>${tag}</span>`).join('')}
                    </div>
                    <a href="${tool.url}" class="card-action-link"><button class="card-action">使用工具</button></a>
                </div>
            `;
            toolGrid.insertAdjacentHTML('beforeend', toolCard);
        });
    }

    // Theme switcher with multiple themes
    const themes = ['light', 'dark', 'ghibli'];
    const themeNames = {
        'light': '浅色主题',
        'dark': '深色主题',
        'ghibli': '吉卜力主题'
    };

    function updateThemeButton(theme) {
        themeToggle.textContent = themeNames[theme];
    }

    themeToggle.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const currentIndex = themes.indexOf(currentTheme);
        const nextIndex = (currentIndex + 1) % themes.length;
        const nextTheme = themes[nextIndex];

        document.documentElement.setAttribute('data-theme', nextTheme);
        localStorage.setItem('theme', nextTheme);
        updateThemeButton(nextTheme);
    });

    // Apply saved theme
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    updateThemeButton(savedTheme);

    // Search functionality
    searchInput.addEventListener('input', () => {
        const activeCategory = document.querySelector('.sidebar nav a.active').dataset.category;
        renderTools(activeCategory, searchInput.value);
    });

    // Category filtering
    categoryLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            categoryLinks.forEach(l => l.classList.remove('active'));
            link.classList.add('active');
            renderTools(link.dataset.category, searchInput.value);
        });
    });

    // Initial render
    renderTools();
});
