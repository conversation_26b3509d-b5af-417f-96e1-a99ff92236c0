<template>
  <svg 
    class="icon-svg" 
    viewBox="0 0 100 100" 
    xmlns="http://www.w3.org/2000/svg"
  >
    <!-- 背景圆圈 -->
    <circle 
      cx="50" 
      cy="50" 
      r="45" 
      fill="#fef3c7" 
      stroke="#f59e0b" 
      stroke-width="2"
      class="bg-circle"
    />
    
    <!-- UUID 容器 -->
    <rect 
      x="20" 
      y="35" 
      width="60" 
      height="30" 
      rx="5" 
      fill="#d97706" 
      stroke="#92400e" 
      stroke-width="2"
      class="uuid-container"
    />
    
    <!-- UUID 分段显示 -->
    <g class="uuid-segments">
      <!-- 第一段 -->
      <rect x="23" y="40" width="12" height="4" rx="1" fill="#fef3c7" class="segment segment1" />
      <!-- 分隔符 -->
      <rect x="37" y="41" width="2" height="2" rx="1" fill="#fef3c7" class="separator sep1" />
      <!-- 第二段 -->
      <rect x="41" y="40" width="8" height="4" rx="1" fill="#fef3c7" class="segment segment2" />
      <!-- 分隔符 -->
      <rect x="51" y="41" width="2" height="2" rx="1" fill="#fef3c7" class="separator sep2" />
      <!-- 第三段 -->
      <rect x="55" y="40" width="8" height="4" rx="1" fill="#fef3c7" class="segment segment3" />
      <!-- 分隔符 -->
      <rect x="65" y="41" width="2" height="2" rx="1" fill="#fef3c7" class="separator sep3" />
      <!-- 第四段 -->
      <rect x="69" y="40" width="8" height="4" rx="1" fill="#fef3c7" class="segment segment4" />
      
      <!-- 第二行 -->
      <rect x="23" y="50" width="16" height="4" rx="1" fill="#fef3c7" class="segment segment5" />
      <rect x="41" y="51" width="2" height="2" rx="1" fill="#fef3c7" class="separator sep4" />
      <rect x="45" y="50" width="20" height="4" rx="1" fill="#fef3c7" class="segment segment6" />
      <rect x="67" y="51" width="2" height="2" rx="1" fill="#fef3c7" class="separator sep5" />
      <rect x="71" y="50" width="6" height="4" rx="1" fill="#fef3c7" class="segment segment7" />
    </g>
    
    <!-- UUID 标签 -->
    <text x="50" y="80" text-anchor="middle" font-family="monospace" font-size="8" fill="#92400e" font-weight="bold" class="uuid-text">UUID</text>
    
    <!-- 生成效果粒子 -->
    <g class="particles">
      <circle cx="15" cy="25" r="1.5" fill="#fbbf24" class="particle p1" />
      <circle cx="85" cy="30" r="1.5" fill="#fbbf24" class="particle p2" />
      <circle cx="10" cy="70" r="1.5" fill="#fbbf24" class="particle p3" />
      <circle cx="90" cy="75" r="1.5" fill="#fbbf24" class="particle p4" />
      <circle cx="25" cy="15" r="1.5" fill="#fbbf24" class="particle p5" />
      <circle cx="75" cy="85" r="1.5" fill="#fbbf24" class="particle p6" />
    </g>
    
    <!-- 魔法棒效果 -->
    <g class="magic-wand">
      <line x1="70" y1="20" x2="80" y2="10" stroke="#f59e0b" stroke-width="2" stroke-linecap="round" class="wand-stick" />
      <circle cx="80" cy="10" r="3" fill="#fbbf24" class="wand-tip" />
      <!-- 魔法星星 -->
      <g class="stars">
        <path d="M75 15 L76 17 L78 17 L76.5 18.5 L77 21 L75 19.5 L73 21 L73.5 18.5 L72 17 L74 17 Z" fill="#fbbf24" class="star star1" />
        <path d="M82 18 L83 19 L84 19 L83.2 19.8 L83.5 21 L82 20.2 L80.5 21 L80.8 19.8 L80 19 L81 19 Z" fill="#fbbf24" class="star star2" />
      </g>
    </g>
  </svg>
</template>

<style scoped>
.icon-svg {
  width: 60px;
  height: 60px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.icon-svg:hover {
  transform: scale(1.1) rotate(3deg);
}

.icon-svg:hover .bg-circle {
  fill: #fde68a;
  stroke: #d97706;
  animation: pulse 1s infinite;
}

.icon-svg:hover .uuid-container {
  animation: containerGlow 1.2s ease-in-out infinite;
}

.icon-svg:hover .segment {
  animation: segmentFill 2s ease-in-out infinite;
}

.icon-svg:hover .segment1 {
  animation-delay: 0.1s;
}

.icon-svg:hover .segment2 {
  animation-delay: 0.2s;
}

.icon-svg:hover .segment3 {
  animation-delay: 0.3s;
}

.icon-svg:hover .segment4 {
  animation-delay: 0.4s;
}

.icon-svg:hover .segment5 {
  animation-delay: 0.5s;
}

.icon-svg:hover .segment6 {
  animation-delay: 0.6s;
}

.icon-svg:hover .segment7 {
  animation-delay: 0.7s;
}

.icon-svg:hover .separator {
  animation: separatorBlink 1s ease-in-out infinite;
}

.icon-svg:hover .uuid-text {
  animation: textPulse 0.8s ease-in-out infinite;
}

.icon-svg:hover .particle {
  animation: particleFloat 3s ease-in-out infinite;
}

.icon-svg:hover .p1 {
  animation-delay: 0s;
}

.icon-svg:hover .p2 {
  animation-delay: 0.5s;
}

.icon-svg:hover .p3 {
  animation-delay: 1s;
}

.icon-svg:hover .p4 {
  animation-delay: 1.5s;
}

.icon-svg:hover .p5 {
  animation-delay: 2s;
}

.icon-svg:hover .p6 {
  animation-delay: 2.5s;
}

.icon-svg:hover .magic-wand {
  animation: wandWave 1.5s ease-in-out infinite;
}

.icon-svg:hover .wand-tip {
  animation: tipSparkle 1s ease-in-out infinite;
}

.icon-svg:hover .star {
  animation: starTwinkle 2s ease-in-out infinite;
}

.icon-svg:hover .star1 {
  animation-delay: 0.2s;
}

.icon-svg:hover .star2 {
  animation-delay: 0.8s;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes containerGlow {
  0%, 100% { filter: drop-shadow(0 0 5px #f59e0b); }
  50% { filter: drop-shadow(0 0 15px #fbbf24); }
}

@keyframes segmentFill {
  0%, 100% { fill: #fef3c7; }
  50% { fill: #fbbf24; }
}

@keyframes separatorBlink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

@keyframes textPulse {
  0%, 100% { font-size: 8px; }
  50% { font-size: 9px; }
}

@keyframes particleFloat {
  0%, 100% { transform: translateY(0) scale(1); }
  33% { transform: translateY(-10px) scale(1.2); }
  66% { transform: translateY(5px) scale(0.8); }
}

@keyframes wandWave {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(10deg); }
  75% { transform: rotate(-10deg); }
}

@keyframes tipSparkle {
  0%, 100% { fill: #fbbf24; transform: scale(1); }
  50% { fill: #f59e0b; transform: scale(1.3); }
}

@keyframes starTwinkle {
  0%, 100% { opacity: 1; transform: scale(1) rotate(0deg); }
  50% { opacity: 0.5; transform: scale(1.5) rotate(180deg); }
}
</style>