开发Vue组件的时候，请参考如下设计指导，来设计界面样式和交互体验：

## Vue组件设计规范

### 界面风格指导
设计一个具有手绘素描风格的创意工具界面，使用Vue 3 Composition API + Tailwind CSS v4.1实现。界面特色包括：
- 使用柔和的纹理纸质背景，带有微妙的网格图案
- 采用柔和的色彩调色板（薄荷绿、柔和的粉色、淡淡的薰衣草色、浅黄色）
- 为重要操作和结果选择性地添加亮色重点
- 实现有趣的Vue过渡动画效果，如操作完成时的动画反馈
- 新内容以渐入动画方式出现，使用Vue的transition组件

### 组件结构要求
- 使用ToolLayout作为基础布局组件
- 使用ResultDisplay组件展示处理结果
- 导航元素设计为彩色标签分隔符（手绘风格）
- 输入控件带有素描风格的边框和图标
- 状态指示器设计为涂鸦星星或旗帜样式

### 装饰元素
包括手绘箭头、类别的小插图以及围绕重要项目的素描高亮效果。使用SVG图标，参考现有的icon组件。

### 布局原则
用清晰定义的区域构建界面，确保主要操作（输入、处理、输出）通过特殊的视觉处理方式突出显示。布局应该感觉像一个专业的工具界面，同时保持一致的创意美学，鼓励用户参与和个性化使用。

### Vue开发要求
- 使用Composition API编写响应式逻辑
- 合理使用composable函数复用逻辑
- 遵循Vue 3最佳实践
- 确保组件的可测试性和可维护性