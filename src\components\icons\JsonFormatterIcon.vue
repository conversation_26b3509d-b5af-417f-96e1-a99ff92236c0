<template>
  <svg 
    class="icon-svg" 
    viewBox="0 0 100 100" 
    xmlns="http://www.w3.org/2000/svg"
  >
    <!-- 背景圆圈 -->
    <circle 
      cx="50" 
      cy="50" 
      r="45" 
      fill="#fef3c7" 
      stroke="#f59e0b" 
      stroke-width="2"
      class="bg-circle"
    />
    
    <!-- JSON 大括号 -->
    <path 
      d="M25 30 Q20 30 20 35 L20 40 Q20 45 15 45 Q20 45 20 50 L20 55 Q20 60 15 60 Q20 60 20 65 L20 70 Q20 75 25 75" 
      fill="none" 
      stroke="#d97706" 
      stroke-width="3" 
      stroke-linecap="round"
      class="left-brace"
    />
    
    <path 
      d="M75 30 Q80 30 80 35 L80 40 Q80 45 85 45 Q80 45 80 50 L80 55 Q80 60 85 60 Q80 60 80 65 L80 70 Q80 75 75 75" 
      fill="none" 
      stroke="#d97706" 
      stroke-width="3" 
      stroke-linecap="round"
      class="right-brace"
    />
    
    <!-- JSON 内容线条 -->
    <line x1="30" y1="40" x2="55" y2="40" stroke="#92400e" stroke-width="2" class="content-line line1" />
    <line x1="30" y1="50" x2="65" y2="50" stroke="#92400e" stroke-width="2" class="content-line line2" />
    <line x1="30" y1="60" x2="50" y2="60" stroke="#92400e" stroke-width="2" class="content-line line3" />
    
    <!-- 装饰性小点 -->
    <circle cx="60" cy="40" r="2" fill="#f59e0b" class="dot dot1" />
    <circle cx="70" cy="50" r="2" fill="#f59e0b" class="dot dot2" />
    <circle cx="55" cy="60" r="2" fill="#f59e0b" class="dot dot3" />
  </svg>
</template>

<style scoped>
.icon-svg {
  width: 60px;
  height: 60px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.icon-svg:hover {
  transform: scale(1.1) rotate(5deg);
}

.icon-svg:hover .bg-circle {
  fill: #fde68a;
  stroke: #d97706;
  animation: pulse 1s infinite;
}

.icon-svg:hover .left-brace {
  animation: slideLeft 0.6s ease-in-out;
}

.icon-svg:hover .right-brace {
  animation: slideRight 0.6s ease-in-out;
}

.icon-svg:hover .content-line {
  animation: fadeInOut 1.2s ease-in-out infinite;
}

.icon-svg:hover .line1 {
  animation-delay: 0s;
}

.icon-svg:hover .line2 {
  animation-delay: 0.2s;
}

.icon-svg:hover .line3 {
  animation-delay: 0.4s;
}

.icon-svg:hover .dot {
  animation: bounce 0.8s ease-in-out infinite;
}

.icon-svg:hover .dot1 {
  animation-delay: 0.1s;
}

.icon-svg:hover .dot2 {
  animation-delay: 0.3s;
}

.icon-svg:hover .dot3 {
  animation-delay: 0.5s;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes slideLeft {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(-3px); }
}

@keyframes slideRight {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(3px); }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}
</style>