<template>
  <svg 
    class="icon-svg" 
    viewBox="0 0 100 100" 
    xmlns="http://www.w3.org/2000/svg"
  >
    <!-- 背景圆圈 -->
    <circle 
      cx="50" 
      cy="50" 
      r="45" 
      fill="#fef3c7" 
      stroke="#f59e0b" 
      stroke-width="2"
      class="bg-circle"
    />
    
    <!-- 锁的主体 -->
    <rect 
      x="35" 
      y="45" 
      width="30" 
      height="25" 
      rx="3" 
      fill="#d97706" 
      class="lock-body"
    />
    
    <!-- 锁的弧形部分 -->
    <path 
      d="M40 45 L40 35 Q40 25 50 25 Q60 25 60 35 L60 45" 
      fill="none" 
      stroke="#d97706" 
      stroke-width="3" 
      stroke-linecap="round"
      class="lock-arc"
    />
    
    <!-- 钥匙孔 -->
    <circle 
      cx="50" 
      cy="55" 
      r="3" 
      fill="#fef3c7" 
      class="keyhole"
    />
    
    <!-- Base64 文字效果 -->
    <text x="50" y="85" text-anchor="middle" font-family="monospace" font-size="8" fill="#92400e" class="base64-text">Base64</text>
    
    <!-- 编码转换箭头 -->
    <path 
      d="M20 20 L25 15 M20 20 L25 25 M20 20 L35 20" 
      stroke="#f59e0b" 
      stroke-width="2" 
      fill="none" 
      stroke-linecap="round"
      class="arrow-left"
    />
    
    <path 
      d="M80 20 L75 15 M80 20 L75 25 M80 20 L65 20" 
      stroke="#f59e0b" 
      stroke-width="2" 
      fill="none" 
      stroke-linecap="round"
      class="arrow-right"
    />
    
    <!-- 装饰性数字 -->
    <text x="15" y="25" font-family="monospace" font-size="6" fill="#d97706" class="number">01</text>
    <text x="80" y="25" font-family="monospace" font-size="6" fill="#d97706" class="number">64</text>
  </svg>
</template>

<style scoped>
.icon-svg {
  width: 60px;
  height: 60px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.icon-svg:hover {
  transform: scale(1.1) rotate(-3deg);
}

.icon-svg:hover .bg-circle {
  fill: #fde68a;
  stroke: #d97706;
  animation: pulse 1s infinite;
}

.icon-svg:hover .lock-body {
  animation: shake 0.6s ease-in-out;
}

.icon-svg:hover .lock-arc {
  animation: unlock 1s ease-in-out;
}

.icon-svg:hover .keyhole {
  animation: glow 1s ease-in-out infinite;
}

.icon-svg:hover .arrow-left {
  animation: slideInLeft 0.8s ease-in-out;
}

.icon-svg:hover .arrow-right {
  animation: slideInRight 0.8s ease-in-out;
}

.icon-svg:hover .base64-text {
  animation: typewriter 1.5s ease-in-out;
}

.icon-svg:hover .number {
  animation: flash 0.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-1px); }
  75% { transform: translateX(1px); }
}

@keyframes unlock {
  0% { stroke-dasharray: 0, 100; }
  50% { stroke-dasharray: 50, 100; }
  100% { stroke-dasharray: 100, 100; }
}

@keyframes glow {
  0%, 100% { fill: #fef3c7; }
  50% { fill: #fbbf24; }
}

@keyframes slideInLeft {
  0% { transform: translateX(-10px); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
  0% { transform: translateX(10px); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes typewriter {
  0% { opacity: 0; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

@keyframes flash {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}
</style>