import { ref } from 'vue'

export function useCopy() {
  const copyStatus = ref('idle') // 'idle', 'copying', 'success', 'error'
  const copyMessage = ref('')

  const copyToClipboard = async (text) => {
    if (!text) {
      copyStatus.value = 'error'
      copyMessage.value = '没有内容可复制'
      return false
    }

    try {
      copyStatus.value = 'copying'
      await navigator.clipboard.writeText(text)
      
      copyStatus.value = 'success'
      copyMessage.value = '已复制到剪贴板'
      
      // 2秒后重置状态
      setTimeout(() => {
        copyStatus.value = 'idle'
        copyMessage.value = ''
      }, 2000)
      
      return true
    } catch (error) {
      console.error('复制失败:', error)
      
      // 降级方案：使用传统方法
      try {
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        
        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)
        
        if (successful) {
          copyStatus.value = 'success'
          copyMessage.value = '已复制到剪贴板'
          
          setTimeout(() => {
            copyStatus.value = 'idle'
            copyMessage.value = ''
          }, 2000)
          
          return true
        } else {
          throw new Error('execCommand failed')
        }
      } catch (fallbackError) {
        copyStatus.value = 'error'
        copyMessage.value = '复制失败，请手动复制'
        
        setTimeout(() => {
          copyStatus.value = 'idle'
          copyMessage.value = ''
        }, 3000)
        
        return false
      }
    }
  }

  const resetCopyStatus = () => {
    copyStatus.value = 'idle'
    copyMessage.value = ''
  }

  return {
    copyStatus,
    copyMessage,
    copyToClipboard,
    resetCopyStatus
  }
}
