# AI 工具集 (AI ToolHub)

这是一个使用原生 HTML, CSS, 和 JavaScript 构建的现代化、响应式 AI 工具集网站。项目旨在提供一个美观、易用的界面，用于集中展示和访问各种 AI 工具。

## ✨ 功能特性

- **数据驱动**: 工具列表通过 JavaScript 动态生成，添加新工具无需修改 HTML。
- **响应式设计**: 完美适配桌面、平板和移动设备。
- **现代化 UI**: 简洁的卡片式布局，支持明/暗两种主题模式。
- **实时搜索**: 在所有工具中快速搜索名称和描述。
- **分类筛选**: 通过侧边栏对工具进行分类查看。
- **轻量高效**: 无需任何外部框架，加载速度快。

## 🚀 快速开始

直接在浏览器中打开 `index.html` 文件即可开始使用。

## 🛠️ 如何添加新工具

添加一个新工具非常简单：

1.  **创建工具页面** (如果需要): 为你的新工具创建一个独立的 HTML 文件。
2.  **注册新工具**: 打开 `js/script.js` 文件。
3.  在文件顶部的 `tools` 数组中，按照以下格式添加一个新对象：

    ```javascript
    {
      name: '你的工具名称',
      description: '关于这个工具的一句话描述。',
      category: 'writing', // 分类: writing, image, video, code
      tags: ['标签1', '标签2'],
      url: 'path/to/your/tool.html' // 指向工具页面的链接
    }
    ```

4.  保存文件，新工具将自动出现在主页上。

## 🏗️ 项目结构

```
/
├── css/
│   └── style.css         # 主要样式文件
├── js/
│   └── script.js         # 核心逻辑，包括工具渲染、搜索、主题切换
├── index.html            # 主页面结构
├── development_plan.md   # 开发计划与扩展方案
├── tools.md              # 工具集网站设计规范
└── README.md             # 项目说明
```

## 📄 开源许可

本项目采用 [Apache 2.0 License](LICENSE) 开源许可。
