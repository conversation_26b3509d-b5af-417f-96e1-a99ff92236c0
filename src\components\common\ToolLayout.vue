<template>
  <div class="tool-layout min-h-screen p-6">
    <div class="max-w-6xl mx-auto">
      <!-- 标题区域 -->
      <div class="text-center mb-8 relative">
        <!-- 装饰星星 -->
        <div class="doodle-star" style="top: 10px; left: 15%;">{{ leftIcon }}</div>
        <div class="doodle-star" style="top: 15px; right: 20%; animation-delay: 1s;">{{ rightIcon }}</div>
        
        <h1 class="text-4xl font-bold text-gray-800 mb-2">{{ title }}</h1>
        <p class="text-lg text-gray-600">{{ description }}</p>
        
        <!-- 工具图标 -->
        <div v-if="showIcon" class="absolute top-16 left-1/2 transform -translate-x-1/2">
          <slot name="icon">
            <div class="tool-main-icon">
              <component :is="iconComponent" v-if="iconComponent" />
            </div>
          </slot>
        </div>
      </div>
      
      <!-- 当前状态显示（可选） -->
      <div v-if="$slots.status" class="current-status mb-6">
        <slot name="status"></slot>
      </div>
      
      <!-- 主要内容区域 -->
      <div class="tool-content">
        <!-- 单列布局 -->
        <div v-if="layout === 'single'" class="single-column">
          <div class="sketch-border bg-white p-6 mb-6">
            <slot name="input"></slot>
          </div>
          
          <div v-if="$slots.output" class="sketch-border bg-white p-6">
            <slot name="output"></slot>
          </div>
        </div>
        
        <!-- 双列布局 -->
        <div v-else-if="layout === 'double'" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="sketch-border bg-white p-6">
            <slot name="input"></slot>
          </div>
          
          <div v-if="$slots.output" class="sketch-border bg-white p-6">
            <slot name="output"></slot>
          </div>
        </div>
        
        <!-- 自定义布局 -->
        <div v-else>
          <slot></slot>
        </div>
      </div>
      
      <!-- 快捷操作区域（可选） -->
      <div v-if="$slots.actions" class="mt-6 sketch-border bg-white p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <span class="mr-2">⚡</span>
          快捷操作
        </h3>
        <slot name="actions"></slot>
      </div>
      
      <!-- 额外信息区域（可选） -->
      <div v-if="$slots.extra" class="mt-6 sketch-border bg-white p-6">
        <slot name="extra"></slot>
      </div>
      
      <!-- 状态信息 -->
      <div v-if="statusMessage" class="mt-4">
        <div :class="statusClass">{{ statusMessage }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  layout: {
    type: String,
    default: 'double', // 'single', 'double', 'custom'
    validator: (value) => ['single', 'double', 'custom'].includes(value)
  },
  leftIcon: {
    type: String,
    default: '⭐'
  },
  rightIcon: {
    type: String,
    default: '✨'
  },
  showIcon: {
    type: Boolean,
    default: false
  },
  iconComponent: {
    type: [String, Object],
    default: null
  },
  statusMessage: {
    type: String,
    default: ''
  },
  statusType: {
    type: String,
    default: 'info',
    validator: (value) => ['success', 'error', 'warning', 'info'].includes(value)
  }
})

const statusClass = computed(() => {
  const baseClass = 'px-4 py-2 rounded-lg text-sm font-medium'
  const typeClasses = {
    success: 'bg-green-50 text-green-700 border border-green-200',
    error: 'bg-red-50 text-red-700 border border-red-200',
    warning: 'bg-yellow-50 text-yellow-700 border border-yellow-200',
    info: 'bg-blue-50 text-blue-700 border border-blue-200'
  }
  return `${baseClass} ${typeClasses[props.statusType]}`
})
</script>

<style scoped>
/* 手绘素描风格 */
.tool-layout {
  background: linear-gradient(135deg, #fef7cd 0%, #fde047 100%);
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.sketch-border {
  border: 2px solid #eab308;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: relative;
}

.sketch-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px dashed #facc15;
  border-radius: 14px;
  pointer-events: none;
}

/* 装饰元素 */
.doodle-star {
  position: absolute;
  color: #eab308;
  font-size: 20px;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { opacity: 1; transform: scale(1) rotate(0deg); }
  50% { opacity: 0.7; transform: scale(1.1) rotate(180deg); }
}

.current-status {
  background: linear-gradient(145deg, #fbbf24, #f59e0b);
  color: white;
  padding: 16px;
  border-radius: 12px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.current-status::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  animation: shine 3s linear infinite;
}

@keyframes shine {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.tool-main-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #eab308;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .tool-layout {
    padding: 1rem;
  }
  
  .doodle-star {
    display: none;
  }
  
  .tool-main-icon {
    position: relative;
    top: 0;
    left: 0;
    transform: none;
    margin: 1rem auto;
  }
}
</style>
