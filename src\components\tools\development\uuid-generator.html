<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UUID生成器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 手绘素描风格 */
        body {
            background: linear-gradient(135deg, #fef7cd 0%, #fde047 100%);
            font-family: 'Comic Sans MS', cursive, sans-serif;
        }
        
        .sketch-border {
            border: 2px solid #eab308;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            position: relative;
        }
        
        .sketch-border::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 1px dashed #facc15;
            border-radius: 14px;
            pointer-events: none;
        }
        
        .btn-sketch {
            background: linear-gradient(145deg, #eab308, #ca8a04);
            border: 2px solid #a16207;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-sketch:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(234, 179, 8, 0.3);
        }
        
        .btn-sketch:active {
            transform: translateY(0);
        }
        
        .input-sketch {
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            background: white;
            transition: border-color 0.3s ease;
        }
        
        .input-sketch:focus {
            outline: none;
            border-color: #eab308;
            box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }
        
        .uuid-card {
            background: linear-gradient(145deg, #fffbeb, #fef3c7);
            border: 2px solid #facc15;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .uuid-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(234, 179, 8, 0.2);
        }
        
        .uuid-text {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
            color: #1f2937;
            background: white;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        
        .error-text {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        .success-text {
            color: #059669;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        /* 装饰元素 */
        .doodle-star {
            position: absolute;
            color: #eab308;
            font-size: 20px;
            animation: sparkle 2s ease-in-out infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { opacity: 1; transform: scale(1) rotate(0deg); }
            50% { opacity: 0.7; transform: scale(1.1) rotate(180deg); }
        }
        
        .uuid-icon {
            stroke: #eab308;
            stroke-width: 2;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
            animation: generate 2s linear infinite;
        }
        
        @keyframes generate {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -40; }
        }
        
        .version-tab {
            padding: 8px 16px;
            border: 2px solid #facc15;
            border-radius: 6px;
            background: white;
            color: #a16207;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .version-tab.active {
            background: linear-gradient(145deg, #eab308, #ca8a04);
            color: white;
            border-color: #a16207;
        }
        
        .version-tab:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(234, 179, 8, 0.2);
        }
        
        .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #eab308;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: #ca8a04;
            transform: scale(1.05);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
            margin-top: 16px;
        }
        
        .stat-item {
            background: white;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #facc15;
            text-align: center;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #a16207;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }
    </style>
</head>
<body class="min-h-screen p-6">
    <div class="max-w-6xl mx-auto">
        <!-- 标题区域 -->
        <div class="text-center mb-8 relative">
            <div class="doodle-star" style="top: 10px; left: 15%;">🆔</div>
            <div class="doodle-star" style="top: 15px; right: 20%; animation-delay: 1s;">🔢</div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">UUID生成器</h1>
            <p class="text-lg text-gray-600">生成全球唯一标识符的专业工具</p>
            
            <!-- UUID图标 -->
            <svg class="absolute top-16 left-1/2 transform -translate-x-1/2" width="80" height="40" viewBox="0 0 80 40">
                <rect class="uuid-icon" x="10" y="15" width="15" height="10" rx="2" stroke-dasharray="5,5" />
                <rect class="uuid-icon" x="30" y="15" width="15" height="10" rx="2" stroke-dasharray="5,5" style="animation-delay: 0.3s;" />
                <rect class="uuid-icon" x="50" y="15" width="15" height="10" rx="2" stroke-dasharray="5,5" style="animation-delay: 0.6s;" />
            </svg>
        </div>
        
        <!-- UUID版本选择 -->
        <div class="flex justify-center mb-6">
            <div class="flex flex-wrap gap-3 bg-white rounded-lg p-3 sketch-border">
                <div id="v1Tab" class="version-tab active" data-version="1">UUID v1</div>
                <div id="v4Tab" class="version-tab" data-version="4">UUID v4</div>
                <div id="nilTab" class="version-tab" data-version="nil">Nil UUID</div>
            </div>
        </div>
        
        <!-- 生成控制区域 -->
        <div class="sketch-border bg-white p-6 mb-6">
            <div class="flex flex-wrap items-center gap-4">
                <div class="flex items-center gap-2">
                    <label class="text-sm font-medium text-gray-700">生成数量：</label>
                    <input 
                        type="number" 
                        id="countInput" 
                        class="input-sketch w-20 text-center" 
                        value="5" 
                        min="1" 
                        max="100"
                    >
                </div>
                
                <div class="flex items-center gap-2">
                    <label class="text-sm font-medium text-gray-700">格式：</label>
                    <select id="formatSelect" class="input-sketch">
                        <option value="default">标准格式</option>
                        <option value="uppercase">大写</option>
                        <option value="lowercase">小写</option>
                        <option value="nodash">无连字符</option>
                        <option value="braces">带花括号</option>
                    </select>
                </div>
                
                <button id="generateBtn" class="btn-sketch">
                    <span class="mr-1">🎲</span>
                    生成UUID
                </button>
                
                <button id="clearAllBtn" class="btn-sketch bg-gradient-to-r from-gray-500 to-gray-600 border-gray-700">
                    <span class="mr-1">🗑️</span>
                    清空全部
                </button>
                
                <button id="copyAllBtn" class="btn-sketch bg-gradient-to-r from-green-500 to-green-600 border-green-700">
                    <span class="mr-1">📋</span>
                    复制全部
                </button>
            </div>
        </div>
        
        <!-- UUID列表 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- UUID显示区域 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">📋</span>
                    生成的UUID
                </h2>
                
                <div id="uuidList" class="space-y-3 max-h-96 overflow-y-auto">
                    <!-- UUID项目将在这里动态生成 -->
                </div>
                
                <!-- 状态信息 -->
                <div id="statusInfo" class="mt-4 text-sm"></div>
            </div>
            
            <!-- 统计和工具区域 -->
            <div class="space-y-6">
                <!-- 统计信息 -->
                <div class="sketch-border bg-white p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <span class="mr-2">📊</span>
                        统计信息
                    </h3>
                    
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div id="totalCount" class="stat-value">0</div>
                            <div class="stat-label">总生成数</div>
                        </div>
                        <div class="stat-item">
                            <div id="currentVersion" class="stat-value">v4</div>
                            <div class="stat-label">当前版本</div>
                        </div>
                        <div class="stat-item">
                            <div id="lastGenTime" class="stat-value">-</div>
                            <div class="stat-label">生成耗时</div>
                        </div>
                    </div>
                </div>
                
                <!-- UUID验证器 -->
                <div class="sketch-border bg-white p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <span class="mr-2">✅</span>
                        UUID验证器
                    </h3>
                    
                    <textarea 
                        id="validateInput" 
                        class="input-sketch w-full h-24 resize-none font-mono text-sm mb-3"
                        placeholder="输入UUID进行验证...\n例如：550e8400-e29b-41d4-a716-************"
                    ></textarea>
                    
                    <button id="validateBtn" class="btn-sketch w-full">
                        <span class="mr-1">🔍</span>
                        验证UUID
                    </button>
                    
                    <div id="validateResult" class="mt-3 text-sm"></div>
                </div>
            </div>
        </div>
        
        <!-- 功能说明 -->
        <div class="mt-8 sketch-border bg-white p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                <span class="mr-2">💡</span>
                UUID版本说明
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">🕐 UUID v1</h4>
                    <p>基于时间戳和MAC地址生成，包含时间信息，适用于需要时间排序的场景</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">🎲 UUID v4</h4>
                    <p>基于随机数生成，最常用的版本，提供良好的唯一性保证</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">⭕ Nil UUID</h4>
                    <p>特殊的空UUID，所有位都为0，用于表示空值或默认值</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        class UUIDGenerator {
            constructor() {
                this.currentVersion = '4';
                this.totalGenerated = 0;
                this.initElements();
                this.bindEvents();
                this.generateInitialUUIDs();
            }
            
            initElements() {
                // 版本选择
                this.v1Tab = document.getElementById('v1Tab');
                this.v4Tab = document.getElementById('v4Tab');
                this.nilTab = document.getElementById('nilTab');
                
                // 控制元素
                this.countInput = document.getElementById('countInput');
                this.formatSelect = document.getElementById('formatSelect');
                this.generateBtn = document.getElementById('generateBtn');
                this.clearAllBtn = document.getElementById('clearAllBtn');
                this.copyAllBtn = document.getElementById('copyAllBtn');
                
                // 显示区域
                this.uuidList = document.getElementById('uuidList');
                this.statusInfo = document.getElementById('statusInfo');
                
                // 统计信息
                this.totalCount = document.getElementById('totalCount');
                this.currentVersionDisplay = document.getElementById('currentVersion');
                this.lastGenTime = document.getElementById('lastGenTime');
                
                // 验证器
                this.validateInput = document.getElementById('validateInput');
                this.validateBtn = document.getElementById('validateBtn');
                this.validateResult = document.getElementById('validateResult');
            }
            
            bindEvents() {
                // 版本切换
                [this.v1Tab, this.v4Tab, this.nilTab].forEach(tab => {
                    tab.addEventListener('click', (e) => this.switchVersion(e.target.dataset.version));
                });
                
                // 主要功能
                this.generateBtn.addEventListener('click', () => this.generateUUIDs());
                this.clearAllBtn.addEventListener('click', () => this.clearAll());
                this.copyAllBtn.addEventListener('click', () => this.copyAll());
                
                // 验证功能
                this.validateBtn.addEventListener('click', () => this.validateUUID());
                this.validateInput.addEventListener('input', () => {
                    this.debounce(() => this.validateUUID(true), 300)();
                });
            }
            
            generateInitialUUIDs() {
                this.generateUUIDs();
            }
            
            switchVersion(version) {
                this.currentVersion = version;
                
                // 更新按钮状态
                document.querySelectorAll('.version-tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelector(`[data-version="${version}"]`).classList.add('active');
                
                // 更新显示
                this.currentVersionDisplay.textContent = version === 'nil' ? 'Nil' : `v${version}`;
            }
            
            generateUUIDs() {
                const startTime = performance.now();
                const count = parseInt(this.countInput.value) || 1;
                const format = this.formatSelect.value;
                
                try {
                    const uuids = [];
                    
                    for (let i = 0; i < count; i++) {
                        let uuid;
                        
                        switch (this.currentVersion) {
                            case '1':
                                uuid = this.generateUUIDv1();
                                break;
                            case '4':
                                uuid = this.generateUUIDv4();
                                break;
                            case 'nil':
                                uuid = this.generateNilUUID();
                                break;
                            default:
                                uuid = this.generateUUIDv4();
                        }
                        
                        uuids.push(this.formatUUID(uuid, format));
                    }
                    
                    this.displayUUIDs(uuids);
                    this.totalGenerated += count;
                    this.updateStats(performance.now() - startTime);
                    
                    this.showStatus(`成功生成 ${count} 个UUID`, 'success');
                    
                } catch (error) {
                    this.showStatus(`生成失败: ${error.message}`, 'error');
                }
            }
            
            generateUUIDv4() {
                return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                    const r = Math.random() * 16 | 0;
                    const v = c === 'x' ? r : (r & 0x3 | 0x8);
                    return v.toString(16);
                });
            }
            
            generateUUIDv1() {
                // 简化的UUID v1实现
                const timestamp = Date.now();
                const timeHex = timestamp.toString(16).padStart(12, '0');
                const clockSeq = Math.random() * 0x3fff | 0;
                const node = Array.from({length: 6}, () => Math.random() * 256 | 0)
                    .map(x => x.toString(16).padStart(2, '0')).join('');
                
                return [
                    timeHex.slice(-8),
                    timeHex.slice(-12, -8),
                    '1' + timeHex.slice(-15, -12),
                    (clockSeq >> 8 | 0x80).toString(16).padStart(2, '0') + 
                    (clockSeq & 0xff).toString(16).padStart(2, '0'),
                    node
                ].join('-');
            }
            
            generateNilUUID() {
                return '00000000-0000-0000-0000-000000000000';
            }
            
            formatUUID(uuid, format) {
                switch (format) {
                    case 'uppercase':
                        return uuid.toUpperCase();
                    case 'lowercase':
                        return uuid.toLowerCase();
                    case 'nodash':
                        return uuid.replace(/-/g, '');
                    case 'braces':
                        return `{${uuid}}`;
                    default:
                        return uuid;
                }
            }
            
            displayUUIDs(uuids) {
                const fragment = document.createDocumentFragment();
                
                uuids.forEach((uuid, index) => {
                    const uuidCard = document.createElement('div');
                    uuidCard.className = 'uuid-card';
                    uuidCard.innerHTML = `
                        <div class="uuid-text">${uuid}</div>
                        <button class="copy-btn" onclick="this.parentElement.copyUUID('${uuid}')">
                            📋 复制
                        </button>
                    `;
                    
                    // 添加复制功能
                    uuidCard.copyUUID = async (uuidText) => {
                        try {
                            await navigator.clipboard.writeText(uuidText);
                            this.showStatus('UUID已复制到剪贴板！', 'success');
                            
                            const btn = uuidCard.querySelector('.copy-btn');
                            const originalText = btn.textContent;
                            btn.textContent = '✅ 已复制';
                            setTimeout(() => {
                                btn.textContent = originalText;
                            }, 2000);
                        } catch (error) {
                            this.showStatus('复制失败，请手动复制', 'error');
                        }
                    };
                    
                    fragment.appendChild(uuidCard);
                });
                
                this.uuidList.appendChild(fragment);
            }
            
            validateUUID(silent = false) {
                const input = this.validateInput.value.trim();
                if (!input) {
                    if (!silent) {
                        this.validateResult.innerHTML = '';
                    }
                    return;
                }
                
                const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
                const nilUuidRegex = /^0{8}-0{4}-0{4}-0{4}-0{12}$/;
                
                let result = '';
                let isValid = false;
                
                if (nilUuidRegex.test(input)) {
                    result = '<div class="success-text">✅ 有效的Nil UUID</div>';
                    isValid = true;
                } else if (uuidRegex.test(input)) {
                    const version = input.charAt(14);
                    result = `<div class="success-text">✅ 有效的UUID v${version}</div>`;
                    isValid = true;
                } else {
                    result = '<div class="error-text">❌ 无效的UUID格式</div>';
                }
                
                this.validateResult.innerHTML = result;
                
                if (!silent && isValid) {
                    this.showStatus('UUID验证完成', 'success');
                }
            }
            
            updateStats(genTime) {
                this.totalCount.textContent = this.totalGenerated;
                this.lastGenTime.textContent = `${genTime.toFixed(2)}ms`;
            }
            
            clearAll() {
                this.uuidList.innerHTML = '';
                this.validateInput.value = '';
                this.validateResult.innerHTML = '';
                this.statusInfo.innerHTML = '';
                this.showStatus('已清空所有UUID', 'success');
            }
            
            async copyAll() {
                const uuidElements = this.uuidList.querySelectorAll('.uuid-text');
                if (uuidElements.length === 0) {
                    this.showStatus('没有可复制的UUID', 'error');
                    return;
                }
                
                const uuids = Array.from(uuidElements).map(el => el.textContent).join('\n');
                
                try {
                    await navigator.clipboard.writeText(uuids);
                    this.showStatus(`已复制 ${uuidElements.length} 个UUID到剪贴板！`, 'success');
                } catch (error) {
                    this.showStatus('复制失败，请手动复制', 'error');
                }
            }
            
            showStatus(message, type) {
                const className = type === 'error' ? 'error-text' : 'success-text';
                this.statusInfo.innerHTML = `<div class="${className}">${message}</div>`;
                
                // 3秒后自动清除状态
                setTimeout(() => {
                    this.statusInfo.innerHTML = '';
                }, 3000);
            }
            
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new UUIDGenerator();
        });
    </script>
</body>
</html>