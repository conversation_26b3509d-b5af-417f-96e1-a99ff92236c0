<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间戳转换器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 手绘素描风格 */
        body {
            background: linear-gradient(135deg, #fef7cd 0%, #fde047 100%);
            font-family: 'Comic Sans MS', cursive, sans-serif;
        }
        
        .sketch-border {
            border: 2px solid #eab308;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            position: relative;
        }
        
        .sketch-border::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 1px dashed #facc15;
            border-radius: 14px;
            pointer-events: none;
        }
        
        .btn-sketch {
            background: linear-gradient(145deg, #eab308, #ca8a04);
            border: 2px solid #a16207;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-sketch:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(234, 179, 8, 0.3);
        }
        
        .btn-sketch:active {
            transform: translateY(0);
        }
        
        .input-sketch {
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            background: white;
            transition: border-color 0.3s ease;
        }
        
        .input-sketch:focus {
            outline: none;
            border-color: #eab308;
            box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }
        
        .result-card {
            background: linear-gradient(145deg, #fffbeb, #fef3c7);
            border: 2px solid #facc15;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(234, 179, 8, 0.2);
        }
        
        .timestamp-display {
            font-family: 'Courier New', monospace;
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            background: white;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .error-text {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        .success-text {
            color: #059669;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        /* 装饰元素 */
        .doodle-star {
            position: absolute;
            color: #eab308;
            font-size: 20px;
            animation: sparkle 2s ease-in-out infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { opacity: 1; transform: scale(1) rotate(0deg); }
            50% { opacity: 0.7; transform: scale(1.1) rotate(180deg); }
        }
        
        .clock-icon {
            stroke: #eab308;
            stroke-width: 2;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
            animation: tick 1s linear infinite;
        }
        
        @keyframes tick {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .format-tab {
            padding: 8px 16px;
            border: 2px solid #facc15;
            border-radius: 6px;
            background: white;
            color: #a16207;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 14px;
        }
        
        .format-tab.active {
            background: linear-gradient(145deg, #eab308, #ca8a04);
            color: white;
            border-color: #a16207;
        }
        
        .format-tab:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(234, 179, 8, 0.2);
        }
        
        .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #eab308;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: #ca8a04;
            transform: scale(1.05);
        }
        
        .timezone-select {
            border: 2px solid #facc15;
            border-radius: 6px;
            padding: 8px 12px;
            background: white;
            color: #374151;
            font-size: 14px;
        }
        
        .current-time {
            background: linear-gradient(145deg, #fbbf24, #f59e0b);
            color: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .current-time::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shine 3s linear infinite;
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .quick-action {
            background: white;
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .quick-action:hover {
            background: #fef3c7;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(234, 179, 8, 0.2);
        }
        
        .format-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }
        
        .format-item {
            background: white;
            border: 1px solid #facc15;
            border-radius: 8px;
            padding: 12px;
        }
        
        .format-label {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 4px;
        }
        
        .format-value {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #1f2937;
            word-break: break-all;
        }
    </style>
</head>
<body class="min-h-screen p-6">
    <div class="max-w-6xl mx-auto">
        <!-- 标题区域 -->
        <div class="text-center mb-8 relative">
            <div class="doodle-star" style="top: 10px; left: 15%;">⏰</div>
            <div class="doodle-star" style="top: 15px; right: 20%; animation-delay: 1s;">🕐</div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">时间戳转换器</h1>
            <p class="text-lg text-gray-600">Unix时间戳与人类可读时间的双向转换工具</p>
            
            <!-- 时钟图标 -->
            <svg class="absolute top-16 left-1/2 transform -translate-x-1/2" width="60" height="60" viewBox="0 0 60 60">
                <circle class="clock-icon" cx="30" cy="30" r="25" />
                <line class="clock-icon" x1="30" y1="30" x2="30" y2="15" style="animation: none;" />
                <line class="clock-icon" x1="30" y1="30" x2="40" y2="30" style="animation: none;" />
                <circle class="clock-icon" cx="30" cy="30" r="2" fill="#eab308" style="animation: none;" />
            </svg>
        </div>
        
        <!-- 当前时间显示 -->
        <div class="current-time">
            <div class="relative z-10">
                <h2 class="text-xl font-bold mb-2">🕐 当前时间</h2>
                <div id="currentTimestamp" class="text-2xl font-mono mb-1">-</div>
                <div id="currentDateTime" class="text-lg">-</div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 时间戳转日期 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">📅</span>
                    时间戳转日期
                </h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">输入时间戳：</label>
                        <input 
                            type="text" 
                            id="timestampInput" 
                            class="input-sketch w-full font-mono" 
                            placeholder="例如：1640995200 或 1640995200000"
                        >
                        <div class="text-xs text-gray-500 mt-1">
                            支持10位（秒）或13位（毫秒）时间戳
                        </div>
                    </div>
                    
                    <div class="flex flex-wrap gap-2">
                        <button id="convertToDateBtn" class="btn-sketch">
                            <span class="mr-1">🔄</span>
                            转换为日期
                        </button>
                        <button id="useCurrentTimestamp" class="btn-sketch bg-gradient-to-r from-blue-500 to-blue-600 border-blue-700">
                            <span class="mr-1">⏰</span>
                            使用当前时间戳
                        </button>
                    </div>
                    
                    <div id="timestampResult" class="space-y-3">
                        <!-- 转换结果将在这里显示 -->
                    </div>
                </div>
            </div>
            
            <!-- 日期转时间戳 -->
            <div class="sketch-border bg-white p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">🔢</span>
                    日期转时间戳
                </h2>
                
                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">日期：</label>
                            <input 
                                type="date" 
                                id="dateInput" 
                                class="input-sketch w-full"
                            >
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">时间：</label>
                            <input 
                                type="time" 
                                id="timeInput" 
                                class="input-sketch w-full"
                                step="1"
                            >
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">时区：</label>
                        <select id="timezoneSelect" class="timezone-select w-full">
                            <option value="local">本地时区</option>
                            <option value="UTC">UTC (协调世界时)</option>
                            <option value="Asia/Shanghai">Asia/Shanghai (北京时间)</option>
                            <option value="America/New_York">America/New_York (纽约时间)</option>
                            <option value="Europe/London">Europe/London (伦敦时间)</option>
                            <option value="Asia/Tokyo">Asia/Tokyo (东京时间)</option>
                        </select>
                    </div>
                    
                    <div class="flex flex-wrap gap-2">
                        <button id="convertToTimestampBtn" class="btn-sketch">
                            <span class="mr-1">🔄</span>
                            转换为时间戳
                        </button>
                        <button id="useCurrentDate" class="btn-sketch bg-gradient-to-r from-green-500 to-green-600 border-green-700">
                            <span class="mr-1">📅</span>
                            使用当前日期
                        </button>
                    </div>
                    
                    <div id="dateResult" class="space-y-3">
                        <!-- 转换结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 快捷操作 -->
        <div class="mt-6 sketch-border bg-white p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <span class="mr-2">⚡</span>
                快捷操作
            </h3>
            
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                <div class="quick-action" data-offset="0">
                    <div class="text-sm font-medium">现在</div>
                    <div class="text-xs text-gray-500">当前时间</div>
                </div>
                <div class="quick-action" data-offset="3600">
                    <div class="text-sm font-medium">1小时后</div>
                    <div class="text-xs text-gray-500">+1h</div>
                </div>
                <div class="quick-action" data-offset="86400">
                    <div class="text-sm font-medium">明天</div>
                    <div class="text-xs text-gray-500">+1d</div>
                </div>
                <div class="quick-action" data-offset="604800">
                    <div class="text-sm font-medium">下周</div>
                    <div class="text-xs text-gray-500">+7d</div>
                </div>
                <div class="quick-action" data-offset="2592000">
                    <div class="text-sm font-medium">下月</div>
                    <div class="text-xs text-gray-500">+30d</div>
                </div>
                <div class="quick-action" data-offset="31536000">
                    <div class="text-sm font-medium">明年</div>
                    <div class="text-xs text-gray-500">+365d</div>
                </div>
            </div>
        </div>
        
        <!-- 常用格式 -->
        <div class="mt-6 sketch-border bg-white p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <span class="mr-2">📋</span>
                常用时间格式
            </h3>
            
            <div id="commonFormats" class="format-grid">
                <!-- 格式将在这里动态生成 -->
            </div>
        </div>
        
        <!-- 状态信息 -->
        <div id="statusInfo" class="mt-4"></div>
    </div>
    
    <script>
        class TimestampConverter {
            constructor() {
                this.initElements();
                this.bindEvents();
                this.updateCurrentTime();
                this.setCurrentDateTime();
                this.updateCommonFormats();
                
                // 每秒更新当前时间
                setInterval(() => this.updateCurrentTime(), 1000);
            }
            
            initElements() {
                // 当前时间显示
                this.currentTimestamp = document.getElementById('currentTimestamp');
                this.currentDateTime = document.getElementById('currentDateTime');
                
                // 时间戳转日期
                this.timestampInput = document.getElementById('timestampInput');
                this.convertToDateBtn = document.getElementById('convertToDateBtn');
                this.useCurrentTimestamp = document.getElementById('useCurrentTimestamp');
                this.timestampResult = document.getElementById('timestampResult');
                
                // 日期转时间戳
                this.dateInput = document.getElementById('dateInput');
                this.timeInput = document.getElementById('timeInput');
                this.timezoneSelect = document.getElementById('timezoneSelect');
                this.convertToTimestampBtn = document.getElementById('convertToTimestampBtn');
                this.useCurrentDate = document.getElementById('useCurrentDate');
                this.dateResult = document.getElementById('dateResult');
                
                // 其他元素
                this.statusInfo = document.getElementById('statusInfo');
                this.commonFormats = document.getElementById('commonFormats');
            }
            
            bindEvents() {
                // 时间戳转换
                this.convertToDateBtn.addEventListener('click', () => this.convertTimestampToDate());
                this.useCurrentTimestamp.addEventListener('click', () => this.useCurrentTimestampValue());
                this.timestampInput.addEventListener('input', () => {
                    this.debounce(() => this.convertTimestampToDate(true), 300)();
                });
                
                // 日期转换
                this.convertToTimestampBtn.addEventListener('click', () => this.convertDateToTimestamp());
                this.useCurrentDate.addEventListener('click', () => this.setCurrentDateTime());
                
                // 快捷操作
                document.querySelectorAll('.quick-action').forEach(action => {
                    action.addEventListener('click', (e) => {
                        const offset = parseInt(e.currentTarget.dataset.offset);
                        this.useQuickAction(offset);
                    });
                });
            }
            
            updateCurrentTime() {
                const now = new Date();
                const timestamp = Math.floor(now.getTime() / 1000);
                
                this.currentTimestamp.textContent = timestamp;
                this.currentDateTime.textContent = this.formatDateTime(now);
            }
            
            setCurrentDateTime() {
                const now = new Date();
                
                // 设置日期
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                this.dateInput.value = `${year}-${month}-${day}`;
                
                // 设置时间
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const seconds = String(now.getSeconds()).padStart(2, '0');
                this.timeInput.value = `${hours}:${minutes}:${seconds}`;
            }
            
            useCurrentTimestampValue() {
                const timestamp = Math.floor(Date.now() / 1000);
                this.timestampInput.value = timestamp;
                this.convertTimestampToDate();
            }
            
            useQuickAction(offset) {
                const timestamp = Math.floor(Date.now() / 1000) + offset;
                this.timestampInput.value = timestamp;
                this.convertTimestampToDate();
            }
            
            convertTimestampToDate(silent = false) {
                const input = this.timestampInput.value.trim();
                if (!input) {
                    if (!silent) {
                        this.timestampResult.innerHTML = '';
                    }
                    return;
                }
                
                try {
                    let timestamp = parseInt(input);
                    
                    // 判断是秒还是毫秒
                    if (timestamp.toString().length === 10) {
                        timestamp *= 1000; // 转换为毫秒
                    } else if (timestamp.toString().length !== 13) {
                        throw new Error('时间戳格式不正确');
                    }
                    
                    const date = new Date(timestamp);
                    
                    if (isNaN(date.getTime())) {
                        throw new Error('无效的时间戳');
                    }
                    
                    this.displayTimestampResult(date, Math.floor(timestamp / 1000));
                    
                    if (!silent) {
                        this.showStatus('时间戳转换成功！', 'success');
                    }
                    
                } catch (error) {
                    if (!silent) {
                        this.showStatus(`转换失败: ${error.message}`, 'error');
                        this.timestampResult.innerHTML = `<div class="error-text">${error.message}</div>`;
                    }
                }
            }
            
            convertDateToTimestamp() {
                const dateValue = this.dateInput.value;
                const timeValue = this.timeInput.value;
                
                if (!dateValue) {
                    this.showStatus('请选择日期', 'error');
                    return;
                }
                
                try {
                    const dateTimeString = `${dateValue}T${timeValue || '00:00:00'}`;
                    let date;
                    
                    const timezone = this.timezoneSelect.value;
                    
                    if (timezone === 'local') {
                        date = new Date(dateTimeString);
                    } else if (timezone === 'UTC') {
                        date = new Date(dateTimeString + 'Z');
                    } else {
                        // 对于其他时区，使用Intl.DateTimeFormat进行处理
                        date = new Date(dateTimeString);
                    }
                    
                    if (isNaN(date.getTime())) {
                        throw new Error('无效的日期时间');
                    }
                    
                    const timestamp = Math.floor(date.getTime() / 1000);
                    this.displayDateResult(date, timestamp);
                    
                    this.showStatus('日期转换成功！', 'success');
                    
                } catch (error) {
                    this.showStatus(`转换失败: ${error.message}`, 'error');
                    this.dateResult.innerHTML = `<div class="error-text">${error.message}</div>`;
                }
            }
            
            displayTimestampResult(date, timestamp) {
                const formats = [
                    { label: '标准格式', value: this.formatDateTime(date) },
                    { label: 'ISO 8601', value: date.toISOString() },
                    { label: 'UTC格式', value: date.toUTCString() },
                    { label: '本地格式', value: date.toLocaleString('zh-CN') },
                    { label: '日期部分', value: date.toLocaleDateString('zh-CN') },
                    { label: '时间部分', value: date.toLocaleTimeString('zh-CN') },
                    { label: '相对时间', value: this.getRelativeTime(date) }
                ];
                
                let html = formats.map(format => `
                    <div class="result-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue('${format.value.replace(/'/g, "\\'")}')">📋</button>
                        <div class="format-label">${format.label}</div>
                        <div class="timestamp-display">${format.value}</div>
                    </div>
                `).join('');
                
                this.timestampResult.innerHTML = html;
                this.addCopyFunctionality(this.timestampResult);
            }
            
            displayDateResult(date, timestamp) {
                const formats = [
                    { label: '秒级时间戳', value: timestamp.toString() },
                    { label: '毫秒级时间戳', value: (timestamp * 1000).toString() },
                    { label: 'JavaScript时间戳', value: date.getTime().toString() },
                    { label: 'Unix时间戳', value: timestamp.toString() }
                ];
                
                let html = formats.map(format => `
                    <div class="result-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue('${format.value}')">📋</button>
                        <div class="format-label">${format.label}</div>
                        <div class="timestamp-display">${format.value}</div>
                    </div>
                `).join('');
                
                this.dateResult.innerHTML = html;
                this.addCopyFunctionality(this.dateResult);
            }
            
            updateCommonFormats() {
                const now = new Date();
                const timestamp = Math.floor(now.getTime() / 1000);
                
                const formats = [
                    { label: 'Unix时间戳 (秒)', value: timestamp.toString() },
                    { label: 'JavaScript时间戳 (毫秒)', value: now.getTime().toString() },
                    { label: 'ISO 8601', value: now.toISOString() },
                    { label: 'RFC 2822', value: now.toUTCString() },
                    { label: 'YYYY-MM-DD', value: now.toISOString().split('T')[0] },
                    { label: 'YYYY-MM-DD HH:mm:ss', value: this.formatDateTime(now) }
                ];
                
                let html = formats.map(format => `
                    <div class="format-item">
                        <div class="format-label">${format.label}</div>
                        <div class="format-value" onclick="this.copyFormatValue('${format.value.replace(/'/g, "\\'")}')"
                             style="cursor: pointer;" title="点击复制">${format.value}</div>
                    </div>
                `).join('');
                
                this.commonFormats.innerHTML = html;
                
                // 每分钟更新一次常用格式
                setTimeout(() => this.updateCommonFormats(), 60000);
            }
            
            addCopyFunctionality(container) {
                container.copyValue = async (value) => {
                    try {
                        await navigator.clipboard.writeText(value);
                        this.showStatus('已复制到剪贴板！', 'success');
                        
                        const btn = event.target;
                        const originalText = btn.textContent;
                        btn.textContent = '✅';
                        setTimeout(() => {
                            btn.textContent = originalText;
                        }, 2000);
                    } catch (error) {
                        this.showStatus('复制失败，请手动复制', 'error');
                    }
                };
            }
            
            formatDateTime(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');
                
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            }
            
            getRelativeTime(date) {
                const now = new Date();
                const diffMs = now.getTime() - date.getTime();
                const diffSec = Math.floor(diffMs / 1000);
                const diffMin = Math.floor(diffSec / 60);
                const diffHour = Math.floor(diffMin / 60);
                const diffDay = Math.floor(diffHour / 24);
                
                if (Math.abs(diffSec) < 60) {
                    return diffSec >= 0 ? `${diffSec}秒前` : `${Math.abs(diffSec)}秒后`;
                } else if (Math.abs(diffMin) < 60) {
                    return diffMin >= 0 ? `${diffMin}分钟前` : `${Math.abs(diffMin)}分钟后`;
                } else if (Math.abs(diffHour) < 24) {
                    return diffHour >= 0 ? `${diffHour}小时前` : `${Math.abs(diffHour)}小时后`;
                } else {
                    return diffDay >= 0 ? `${diffDay}天前` : `${Math.abs(diffDay)}天后`;
                }
            }
            
            showStatus(message, type) {
                const className = type === 'error' ? 'error-text' : 'success-text';
                this.statusInfo.innerHTML = `<div class="${className}">${message}</div>`;
                
                // 3秒后自动清除状态
                setTimeout(() => {
                    this.statusInfo.innerHTML = '';
                }, 3000);
            }
            
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        }
        
        // 全局复制函数
        window.copyFormatValue = async function(value) {
            try {
                await navigator.clipboard.writeText(value);
                // 简单的视觉反馈
                event.target.style.background = '#dcfce7';
                setTimeout(() => {
                    event.target.style.background = '';
                }, 1000);
            } catch (error) {
                console.error('复制失败:', error);
            }
        };
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new TimestampConverter();
        });
    </script>
</body>
</html>