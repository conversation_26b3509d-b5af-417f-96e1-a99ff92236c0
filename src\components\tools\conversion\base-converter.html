<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进制转换器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 手绘素描风格 */
        body {
            background: linear-gradient(135deg, #fef7cd 0%, #fde047 100%);
            font-family: 'Comic Sans MS', cursive, sans-serif;
        }
        
        .sketch-border {
            border: 2px solid #eab308;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            position: relative;
        }
        
        .sketch-border::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 1px dashed #facc15;
            border-radius: 14px;
            pointer-events: none;
        }
        
        .btn-sketch {
            background: linear-gradient(145deg, #eab308, #ca8a04);
            border: 2px solid #a16207;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-sketch:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(234, 179, 8, 0.3);
        }
        
        .btn-sketch:active {
            transform: translateY(0);
        }
        
        .input-sketch {
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            background: white;
            transition: border-color 0.3s ease;
        }
        
        .input-sketch:focus {
            outline: none;
            border-color: #eab308;
            box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }
        
        .base-card {
            background: linear-gradient(145deg, #fffbeb, #fef3c7);
            border: 2px solid #facc15;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .base-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(234, 179, 8, 0.2);
        }
        
        .base-display {
            font-family: 'Courier New', monospace;
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            background: white;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            word-break: break-all;
            min-height: 48px;
            display: flex;
            align-items: center;
        }
        
        .error-text {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        .success-text {
            color: #059669;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            padding: 8px 12px;
        }
        
        /* 装饰元素 */
        .doodle-star {
            position: absolute;
            color: #eab308;
            font-size: 20px;
            animation: sparkle 2s ease-in-out infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { opacity: 1; transform: scale(1) rotate(0deg); }
            50% { opacity: 0.7; transform: scale(1.1) rotate(180deg); }
        }
        
        .binary-icon {
            stroke: #eab308;
            stroke-width: 2;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
            animation: flow 2s linear infinite;
        }
        
        @keyframes flow {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -20; }
        }
        
        .base-tab {
            padding: 8px 16px;
            border: 2px solid #facc15;
            border-radius: 6px;
            background: white;
            color: #a16207;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 14px;
        }
        
        .base-tab.active {
            background: linear-gradient(145deg, #eab308, #ca8a04);
            color: white;
            border-color: #a16207;
        }
        
        .base-tab:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(234, 179, 8, 0.2);
        }
        
        .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #eab308;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: #ca8a04;
            transform: scale(1.05);
        }
        
        .base-label {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .base-info {
            font-size: 12px;
            color: #6b7280;
            font-weight: normal;
        }
        
        .quick-convert {
            background: white;
            border: 2px solid #facc15;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .quick-convert:hover {
            background: #fef3c7;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(234, 179, 8, 0.2);
        }
        
        .calculation-steps {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px;
            margin-top: 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #475569;
        }
        
        .step-item {
            margin-bottom: 4px;
            padding: 2px 0;
        }
        
        .step-item:last-child {
            margin-bottom: 0;
            font-weight: bold;
            color: #1e293b;
        }
        
        .custom-base {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 12px;
        }
        
        .custom-base input {
            width: 80px;
        }
        
        .bit-display {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 4px;
            margin-top: 8px;
        }
        
        .bit-item {
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 4px;
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            font-weight: bold;
        }
        
        .bit-item.active {
            background: #eab308;
            color: white;
            border-color: #a16207;
        }
    </style>
</head>
<body class="min-h-screen p-6">
    <div class="max-w-6xl mx-auto">
        <!-- 标题区域 -->
        <div class="text-center mb-8 relative">
            <div class="doodle-star" style="top: 10px; left: 15%;">🔢</div>
            <div class="doodle-star" style="top: 15px; right: 20%; animation-delay: 1s;">💻</div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">进制转换器</h1>
            <p class="text-lg text-gray-600">支持2-36进制之间的数值转换</p>
            
            <!-- 二进制图标 -->
            <svg class="absolute top-16 left-1/2 transform -translate-x-1/2" width="80" height="40" viewBox="0 0 80 40">
                <text class="binary-icon" x="10" y="25" font-family="monospace" font-size="16" stroke-dasharray="2,2">101</text>
                <text class="binary-icon" x="40" y="25" font-family="monospace" font-size="16" stroke-dasharray="2,2" style="animation-delay: 0.5s;">010</text>
            </svg>
        </div>
        
        <!-- 输入区域 -->
        <div class="sketch-border bg-white p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <span class="mr-2">📝</span>
                输入数值
            </h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 items-end">
                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">数值：</label>
                    <input 
                        type="text" 
                        id="numberInput" 
                        class="input-sketch w-full font-mono text-lg" 
                        placeholder="输入要转换的数值..."
                    >
                    <div class="text-xs text-gray-500 mt-1">
                        支持整数和小数，负数请加负号
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">源进制：</label>
                    <select id="sourceBase" class="input-sketch w-full">
                        <option value="10">十进制 (10)</option>
                        <option value="2">二进制 (2)</option>
                        <option value="8">八进制 (8)</option>
                        <option value="16">十六进制 (16)</option>
                        <option value="custom">自定义进制</option>
                    </select>
                    
                    <div id="customSourceBase" class="custom-base" style="display: none;">
                        <label class="text-sm">进制：</label>
                        <input type="number" id="customSourceValue" class="input-sketch" min="2" max="36" value="10">
                    </div>
                </div>
            </div>
            
            <div class="flex flex-wrap gap-3 mt-4">
                <button id="convertBtn" class="btn-sketch">
                    <span class="mr-1">🔄</span>
                    转换
                </button>
                <button id="clearBtn" class="btn-sketch bg-gradient-to-r from-gray-500 to-gray-600 border-gray-700">
                    <span class="mr-1">🗑️</span>
                    清空
                </button>
                <button id="swapBtn" class="btn-sketch bg-gradient-to-r from-blue-500 to-blue-600 border-blue-700">
                    <span class="mr-1">🔄</span>
                    交换进制
                </button>
            </div>
        </div>
        
        <!-- 转换结果 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 常用进制 -->
            <div class="sketch-border bg-white p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="mr-2">📊</span>
                    常用进制
                </h3>
                
                <div class="space-y-3">
                    <div class="base-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="base-label">
                            二进制 (Binary)
                            <span class="base-info">基数: 2, 字符: 0-1</span>
                        </div>
                        <div id="binaryResult" class="base-display">-</div>
                        <div id="binaryBits" class="bit-display" style="display: none;"></div>
                    </div>
                    
                    <div class="base-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="base-label">
                            八进制 (Octal)
                            <span class="base-info">基数: 8, 字符: 0-7</span>
                        </div>
                        <div id="octalResult" class="base-display">-</div>
                    </div>
                    
                    <div class="base-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="base-label">
                            十进制 (Decimal)
                            <span class="base-info">基数: 10, 字符: 0-9</span>
                        </div>
                        <div id="decimalResult" class="base-display">-</div>
                    </div>
                    
                    <div class="base-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="base-label">
                            十六进制 (Hexadecimal)
                            <span class="base-info">基数: 16, 字符: 0-9, A-F</span>
                        </div>
                        <div id="hexResult" class="base-display">-</div>
                    </div>
                </div>
            </div>
            
            <!-- 自定义进制和计算步骤 -->
            <div class="space-y-6">
                <!-- 自定义进制转换 -->
                <div class="sketch-border bg-white p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="mr-2">⚙️</span>
                        自定义进制转换
                    </h3>
                    
                    <div class="grid grid-cols-2 gap-3 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">目标进制：</label>
                            <input type="number" id="targetBase" class="input-sketch w-full" min="2" max="36" value="16">
                        </div>
                        <div class="flex items-end">
                            <button id="customConvertBtn" class="btn-sketch w-full">
                                转换
                            </button>
                        </div>
                    </div>
                    
                    <div class="base-card">
                        <button class="copy-btn" onclick="this.parentElement.copyValue()">📋</button>
                        <div class="base-label">
                            <span id="customBaseLabel">自定义进制结果</span>
                        </div>
                        <div id="customResult" class="base-display">-</div>
                    </div>
                </div>
                
                <!-- 计算步骤 -->
                <div class="sketch-border bg-white p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="mr-2">🧮</span>
                        计算步骤
                    </h3>
                    
                    <div id="calculationSteps" class="calculation-steps">
                        <div class="text-center text-gray-500">输入数值后显示计算步骤</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 快速转换 -->
        <div class="mt-6 sketch-border bg-white p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <span class="mr-2">⚡</span>
                快速转换
            </h3>
            
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                <div class="quick-convert" data-value="255" data-base="10">
                    <div class="text-sm font-medium">255</div>
                    <div class="text-xs text-gray-500">十进制</div>
                </div>
                <div class="quick-convert" data-value="11111111" data-base="2">
                    <div class="text-sm font-medium">11111111</div>
                    <div class="text-xs text-gray-500">二进制</div>
                </div>
                <div class="quick-convert" data-value="FF" data-base="16">
                    <div class="text-sm font-medium">FF</div>
                    <div class="text-xs text-gray-500">十六进制</div>
                </div>
                <div class="quick-convert" data-value="1024" data-base="10">
                    <div class="text-sm font-medium">1024</div>
                    <div class="text-xs text-gray-500">十进制</div>
                </div>
                <div class="quick-convert" data-value="777" data-base="8">
                    <div class="text-sm font-medium">777</div>
                    <div class="text-xs text-gray-500">八进制</div>
                </div>
                <div class="quick-convert" data-value="ABCD" data-base="16">
                    <div class="text-sm font-medium">ABCD</div>
                    <div class="text-xs text-gray-500">十六进制</div>
                </div>
            </div>
        </div>
        
        <!-- 状态信息 -->
        <div id="statusInfo" class="mt-4"></div>
    </div>
    
    <script>
        class BaseConverter {
            constructor() {
                this.initElements();
                this.bindEvents();
            }
            
            initElements() {
                // 输入元素
                this.numberInput = document.getElementById('numberInput');
                this.sourceBase = document.getElementById('sourceBase');
                this.customSourceBase = document.getElementById('customSourceBase');
                this.customSourceValue = document.getElementById('customSourceValue');
                this.targetBase = document.getElementById('targetBase');
                
                // 按钮
                this.convertBtn = document.getElementById('convertBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.swapBtn = document.getElementById('swapBtn');
                this.customConvertBtn = document.getElementById('customConvertBtn');
                
                // 结果显示
                this.binaryResult = document.getElementById('binaryResult');
                this.octalResult = document.getElementById('octalResult');
                this.decimalResult = document.getElementById('decimalResult');
                this.hexResult = document.getElementById('hexResult');
                this.customResult = document.getElementById('customResult');
                this.customBaseLabel = document.getElementById('customBaseLabel');
                
                // 其他元素
                this.binaryBits = document.getElementById('binaryBits');
                this.calculationSteps = document.getElementById('calculationSteps');
                this.statusInfo = document.getElementById('statusInfo');
            }
            
            bindEvents() {
                // 主要功能
                this.convertBtn.addEventListener('click', () => this.convertNumber());
                this.clearBtn.addEventListener('click', () => this.clearAll());
                this.swapBtn.addEventListener('click', () => this.swapBases());
                this.customConvertBtn.addEventListener('click', () => this.customConvert());
                
                // 输入监听
                this.numberInput.addEventListener('input', () => {
                    this.debounce(() => this.convertNumber(true), 300)();
                });
                
                // 进制选择
                this.sourceBase.addEventListener('change', () => {
                    this.toggleCustomBase();
                    this.convertNumber(true);
                });
                
                this.customSourceValue.addEventListener('input', () => {
                    this.convertNumber(true);
                });
                
                this.targetBase.addEventListener('input', () => {
                    this.updateCustomLabel();
                });
                
                // 快速转换
                document.querySelectorAll('.quick-convert').forEach(item => {
                    item.addEventListener('click', (e) => {
                        const value = e.currentTarget.dataset.value;
                        const base = e.currentTarget.dataset.base;
                        this.useQuickValue(value, base);
                    });
                });
                
                this.updateCustomLabel();
            }
            
            toggleCustomBase() {
                const isCustom = this.sourceBase.value === 'custom';
                this.customSourceBase.style.display = isCustom ? 'flex' : 'none';
            }
            
            updateCustomLabel() {
                const base = this.targetBase.value;
                this.customBaseLabel.textContent = `${base}进制结果`;
            }
            
            useQuickValue(value, base) {
                this.numberInput.value = value;
                this.sourceBase.value = base;
                this.toggleCustomBase();
                this.convertNumber();
            }
            
            swapBases() {
                // 简单的二进制和十六进制交换示例
                if (this.sourceBase.value === '2') {
                    this.sourceBase.value = '16';
                } else if (this.sourceBase.value === '16') {
                    this.sourceBase.value = '2';
                } else {
                    this.sourceBase.value = this.sourceBase.value === '10' ? '16' : '10';
                }
                this.toggleCustomBase();
                this.convertNumber();
            }
            
            convertNumber(silent = false) {
                const input = this.numberInput.value.trim();
                if (!input) {
                    if (!silent) {
                        this.clearResults();
                    }
                    return;
                }
                
                try {
                    const sourceBase = this.getSourceBase();
                    
                    // 验证输入是否符合源进制
                    if (!this.validateInput(input, sourceBase)) {
                        throw new Error(`输入不符合${sourceBase}进制格式`);
                    }
                    
                    // 转换为十进制
                    const decimalValue = this.toDecimal(input, sourceBase);
                    
                    // 转换为各种进制
                    this.displayResults(decimalValue, input, sourceBase);
                    
                    if (!silent) {
                        this.showStatus('转换成功！', 'success');
                    }
                    
                } catch (error) {
                    if (!silent) {
                        this.showStatus(`转换失败: ${error.message}`, 'error');
                        this.clearResults();
                    }
                }
            }
            
            customConvert() {
                const input = this.numberInput.value.trim();
                if (!input) {
                    this.showStatus('请输入要转换的数值', 'error');
                    return;
                }
                
                try {
                    const sourceBase = this.getSourceBase();
                    const targetBase = parseInt(this.targetBase.value);
                    
                    if (targetBase < 2 || targetBase > 36) {
                        throw new Error('目标进制必须在2-36之间');
                    }
                    
                    const decimalValue = this.toDecimal(input, sourceBase);
                    const result = this.fromDecimal(decimalValue, targetBase);
                    
                    this.customResult.textContent = result;
                    this.addCopyFunctionality(this.customResult.parentElement, result);
                    
                    this.showStatus(`成功转换为${targetBase}进制！`, 'success');
                    
                } catch (error) {
                    this.showStatus(`转换失败: ${error.message}`, 'error');
                }
            }
            
            getSourceBase() {
                if (this.sourceBase.value === 'custom') {
                    return parseInt(this.customSourceValue.value) || 10;
                }
                return parseInt(this.sourceBase.value);
            }
            
            validateInput(input, base) {
                const cleanInput = input.replace(/^-/, ''); // 移除负号
                const validChars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'.slice(0, base);
                
                return cleanInput.split('').every(char => 
                    validChars.includes(char.toUpperCase()) || char === '.'
                );
            }
            
            toDecimal(input, base) {
                const isNegative = input.startsWith('-');
                const cleanInput = input.replace(/^-/, '').toUpperCase();
                
                if (cleanInput.includes('.')) {
                    // 处理小数
                    const [intPart, fracPart] = cleanInput.split('.');
                    let result = 0;
                    
                    // 整数部分
                    for (let i = 0; i < intPart.length; i++) {
                        const digit = this.charToDigit(intPart[intPart.length - 1 - i]);
                        result += digit * Math.pow(base, i);
                    }
                    
                    // 小数部分
                    for (let i = 0; i < fracPart.length; i++) {
                        const digit = this.charToDigit(fracPart[i]);
                        result += digit * Math.pow(base, -(i + 1));
                    }
                    
                    return isNegative ? -result : result;
                } else {
                    // 处理整数
                    let result = 0;
                    for (let i = 0; i < cleanInput.length; i++) {
                        const digit = this.charToDigit(cleanInput[cleanInput.length - 1 - i]);
                        result += digit * Math.pow(base, i);
                    }
                    return isNegative ? -result : result;
                }
            }
            
            fromDecimal(decimal, base) {
                if (decimal === 0) return '0';
                
                const isNegative = decimal < 0;
                let num = Math.abs(decimal);
                
                if (num % 1 !== 0) {
                    // 处理小数
                    const intPart = Math.floor(num);
                    const fracPart = num - intPart;
                    
                    let intResult = this.intToBase(intPart, base);
                    let fracResult = this.fracToBase(fracPart, base);
                    
                    return (isNegative ? '-' : '') + intResult + (fracResult ? '.' + fracResult : '');
                } else {
                    // 处理整数
                    const result = this.intToBase(num, base);
                    return (isNegative ? '-' : '') + result;
                }
            }
            
            intToBase(num, base) {
                if (num === 0) return '0';
                
                let result = '';
                while (num > 0) {
                    result = this.digitToChar(num % base) + result;
                    num = Math.floor(num / base);
                }
                return result;
            }
            
            fracToBase(frac, base, precision = 10) {
                if (frac === 0) return '';
                
                let result = '';
                let count = 0;
                
                while (frac > 0 && count < precision) {
                    frac *= base;
                    const digit = Math.floor(frac);
                    result += this.digitToChar(digit);
                    frac -= digit;
                    count++;
                }
                
                return result;
            }
            
            charToDigit(char) {
                if (char >= '0' && char <= '9') {
                    return char.charCodeAt(0) - '0'.charCodeAt(0);
                }
                return char.charCodeAt(0) - 'A'.charCodeAt(0) + 10;
            }
            
            digitToChar(digit) {
                if (digit < 10) {
                    return String.fromCharCode('0'.charCodeAt(0) + digit);
                }
                return String.fromCharCode('A'.charCodeAt(0) + digit - 10);
            }
            
            displayResults(decimalValue, originalInput, sourceBase) {
                // 显示常用进制结果
                this.binaryResult.textContent = this.fromDecimal(decimalValue, 2);
                this.octalResult.textContent = this.fromDecimal(decimalValue, 8);
                this.decimalResult.textContent = decimalValue.toString();
                this.hexResult.textContent = this.fromDecimal(decimalValue, 16);
                
                // 添加复制功能
                this.addCopyFunctionality(this.binaryResult.parentElement, this.binaryResult.textContent);
                this.addCopyFunctionality(this.octalResult.parentElement, this.octalResult.textContent);
                this.addCopyFunctionality(this.decimalResult.parentElement, this.decimalResult.textContent);
                this.addCopyFunctionality(this.hexResult.parentElement, this.hexResult.textContent);
                
                // 显示二进制位
                this.displayBinaryBits(decimalValue);
                
                // 显示计算步骤
                this.displayCalculationSteps(originalInput, sourceBase, decimalValue);
            }
            
            displayBinaryBits(decimalValue) {
                if (decimalValue >= 0 && decimalValue <= 255 && decimalValue % 1 === 0) {
                    this.binaryBits.style.display = 'grid';
                    const binary = this.fromDecimal(decimalValue, 2).padStart(8, '0');
                    
                    this.binaryBits.innerHTML = '';
                    for (let i = 0; i < 8; i++) {
                        const bit = document.createElement('div');
                        bit.className = `bit-item ${binary[i] === '1' ? 'active' : ''}`;
                        bit.textContent = binary[i];
                        this.binaryBits.appendChild(bit);
                    }
                } else {
                    this.binaryBits.style.display = 'none';
                }
            }
            
            displayCalculationSteps(input, sourceBase, result) {
                const steps = [];
                
                if (sourceBase !== 10) {
                    steps.push(`原始输入: ${input} (${sourceBase}进制)`);
                    steps.push(`转换为十进制:`);
                    
                    const cleanInput = input.replace(/^-/, '').toUpperCase();
                    if (!cleanInput.includes('.')) {
                        // 整数转换步骤
                        let calculation = '';
                        for (let i = 0; i < cleanInput.length; i++) {
                            const digit = this.charToDigit(cleanInput[cleanInput.length - 1 - i]);
                            const power = i;
                            if (i > 0) calculation += ' + ';
                            calculation += `${digit}×${sourceBase}^${power}`;
                        }
                        steps.push(`= ${calculation}`);
                    }
                    
                    steps.push(`= ${result} (十进制)`);
                } else {
                    steps.push(`输入: ${input} (十进制)`);
                }
                
                steps.push('');
                steps.push('转换为其他进制:');
                steps.push(`二进制: ${this.fromDecimal(result, 2)}`);
                steps.push(`八进制: ${this.fromDecimal(result, 8)}`);
                steps.push(`十六进制: ${this.fromDecimal(result, 16)}`);
                
                this.calculationSteps.innerHTML = steps.map(step => 
                    `<div class="step-item">${step}</div>`
                ).join('');
            }
            
            addCopyFunctionality(container, value) {
                container.copyValue = async () => {
                    try {
                        await navigator.clipboard.writeText(value);
                        this.showStatus('已复制到剪贴板！', 'success');
                        
                        const btn = container.querySelector('.copy-btn');
                        const originalText = btn.textContent;
                        btn.textContent = '✅';
                        setTimeout(() => {
                            btn.textContent = originalText;
                        }, 2000);
                    } catch (error) {
                        this.showStatus('复制失败，请手动复制', 'error');
                    }
                };
            }
            
            clearResults() {
                this.binaryResult.textContent = '-';
                this.octalResult.textContent = '-';
                this.decimalResult.textContent = '-';
                this.hexResult.textContent = '-';
                this.customResult.textContent = '-';
                this.binaryBits.style.display = 'none';
                this.calculationSteps.innerHTML = '<div class="text-center text-gray-500">输入数值后显示计算步骤</div>';
            }
            
            clearAll() {
                this.numberInput.value = '';
                this.sourceBase.value = '10';
                this.targetBase.value = '16';
                this.toggleCustomBase();
                this.updateCustomLabel();
                this.clearResults();
                this.statusInfo.innerHTML = '';
                this.showStatus('已清空所有内容', 'success');
            }
            
            showStatus(message, type) {
                const className = type === 'error' ? 'error-text' : 'success-text';
                this.statusInfo.innerHTML = `<div class="${className}">${message}</div>`;
                
                // 3秒后自动清除状态
                setTimeout(() => {
                    this.statusInfo.innerHTML = '';
                }, 3000);
            }
            
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new BaseConverter();
        });
    </script>
</body>
</html>