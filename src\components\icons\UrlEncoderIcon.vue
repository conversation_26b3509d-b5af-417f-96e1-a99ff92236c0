<template>
  <svg 
    class="icon-svg" 
    viewBox="0 0 100 100" 
    xmlns="http://www.w3.org/2000/svg"
  >
    <!-- 背景圆圈 -->
    <circle 
      cx="50" 
      cy="50" 
      r="45" 
      fill="#fef3c7" 
      stroke="#f59e0b" 
      stroke-width="2"
      class="bg-circle"
    />
    
    <!-- 链条链接 -->
    <g class="chain">
      <!-- 第一个链环 -->
      <ellipse cx="35" cy="40" rx="8" ry="12" fill="none" stroke="#d97706" stroke-width="3" class="link link1" />
      
      <!-- 第二个链环 -->
      <ellipse cx="50" cy="40" rx="8" ry="12" fill="none" stroke="#d97706" stroke-width="3" class="link link2" />
      
      <!-- 第三个链环 -->
      <ellipse cx="65" cy="40" rx="8" ry="12" fill="none" stroke="#d97706" stroke-width="3" class="link link3" />
    </g>
    
    <!-- URL 文字 -->
    <text x="50" y="65" text-anchor="middle" font-family="monospace" font-size="10" fill="#92400e" font-weight="bold" class="url-text">URL</text>
    
    <!-- 编码符号 % -->
    <g class="percent-symbols">
      <text x="25" y="25" font-family="monospace" font-size="12" fill="#f59e0b" class="percent percent1">%</text>
      <text x="70" y="25" font-family="monospace" font-size="12" fill="#f59e0b" class="percent percent2">%</text>
      <text x="15" y="75" font-family="monospace" font-size="10" fill="#f59e0b" class="percent percent3">%20</text>
      <text x="75" y="75" font-family="monospace" font-size="10" fill="#f59e0b" class="percent percent4">%3A</text>
    </g>
    
    <!-- 转换箭头 -->
    <path 
      d="M30 55 Q50 50 70 55" 
      fill="none" 
      stroke="#d97706" 
      stroke-width="2" 
      marker-end="url(#arrowhead)" 
      class="conversion-arrow"
    />
    
    <!-- 箭头标记定义 -->
    <defs>
      <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
        <polygon points="0 0, 10 3.5, 0 7" fill="#d97706" class="arrow-tip" />
      </marker>
    </defs>
    
    <!-- 装饰性数据点 -->
    <circle cx="20" cy="50" r="2" fill="#fbbf24" class="data-point point1" />
    <circle cx="80" cy="50" r="2" fill="#fbbf24" class="data-point point2" />
    <circle cx="50" cy="20" r="2" fill="#fbbf24" class="data-point point3" />
    <circle cx="50" cy="80" r="2" fill="#fbbf24" class="data-point point4" />
  </svg>
</template>

<style scoped>
.icon-svg {
  width: 60px;
  height: 60px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.icon-svg:hover {
  transform: scale(1.1) rotate(-2deg);
}

.icon-svg:hover .bg-circle {
  fill: #fde68a;
  stroke: #d97706;
  animation: pulse 1s infinite;
}

.icon-svg:hover .chain {
  animation: chainMove 1.5s ease-in-out infinite;
}

.icon-svg:hover .link {
  animation: linkPulse 1s ease-in-out infinite;
}

.icon-svg:hover .link1 {
  animation-delay: 0s;
}

.icon-svg:hover .link2 {
  animation-delay: 0.2s;
}

.icon-svg:hover .link3 {
  animation-delay: 0.4s;
}

.icon-svg:hover .url-text {
  animation: textBounce 0.8s ease-in-out infinite;
}

.icon-svg:hover .percent {
  animation: percentFloat 2s ease-in-out infinite;
}

.icon-svg:hover .percent1 {
  animation-delay: 0s;
}

.icon-svg:hover .percent2 {
  animation-delay: 0.5s;
}

.icon-svg:hover .percent3 {
  animation-delay: 1s;
}

.icon-svg:hover .percent4 {
  animation-delay: 1.5s;
}

.icon-svg:hover .conversion-arrow {
  animation: arrowFlow 2s ease-in-out infinite;
}

.icon-svg:hover .arrow-tip {
  animation: tipPulse 1s ease-in-out infinite;
}

.icon-svg:hover .data-point {
  animation: pointOrbit 3s linear infinite;
}

.icon-svg:hover .point1 {
  animation-delay: 0s;
}

.icon-svg:hover .point2 {
  animation-delay: 0.75s;
}

.icon-svg:hover .point3 {
  animation-delay: 1.5s;
}

.icon-svg:hover .point4 {
  animation-delay: 2.25s;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes chainMove {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

@keyframes linkPulse {
  0%, 100% { stroke-width: 3; }
  50% { stroke-width: 4; }
}

@keyframes textBounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

@keyframes percentFloat {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-5px) scale(1.1); }
}

@keyframes arrowFlow {
  0% { stroke-dasharray: 0, 100; }
  50% { stroke-dasharray: 50, 100; }
  100% { stroke-dasharray: 100, 0; }
}

@keyframes tipPulse {
  0%, 100% { fill: #d97706; }
  50% { fill: #f59e0b; }
}

@keyframes pointOrbit {
  0% { transform: scale(1); }
  25% { transform: scale(1.5); }
  50% { transform: scale(1); }
  75% { transform: scale(0.8); }
  100% { transform: scale(1); }
}
</style>