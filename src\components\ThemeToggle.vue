<template>
  <div class="theme-switcher">
    <select v-model="selectedTheme" @change="changeTheme">
      <option value="light">☀️ 手稿主题</option>
      <option value="dark">🌙 暗黑主题</option>
      <option value="ghibli">🌿 吉卜力主题</option>
    </select>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useTheme } from '../composables/useTheme.js'

const { theme, setTheme } = useTheme()
const selectedTheme = ref(theme.value)

const changeTheme = () => {
  setTheme(selectedTheme.value)
}

watch(theme, (newTheme) => {
  selectedTheme.value = newTheme
})
</script>

<style scoped>
.theme-switcher {
  flex-shrink: 0;
  position: relative;
}

/* 添加装饰性图标 */
.theme-switcher::before {
  content: '🎨';
  position: absolute;
  left: -2rem;
  top: 50%;
  transform: translateY(-50%) rotate(15deg);
  font-size: 1.2rem;
  opacity: 0.6;
  z-index: 1;
  pointer-events: none;
  animation: palette-spin 4s ease-in-out infinite;
}

@keyframes palette-spin {

  0%,
  100% {
    transform: translateY(-50%) rotate(15deg);
  }

  50% {
    transform: translateY(-50%) rotate(-15deg);
  }
}

.theme-switcher select {
  padding: 0.8rem 1rem;
  border: 3px dashed #B7DFBA;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(247, 244, 233, 0.9) 100%);
  color: #4A4038;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 700;
  font-family: 'Comic Sans MS', 'Chalkduster', 'Marker Felt', cursive, sans-serif;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  min-width: 140px;
  transform: rotate(-1deg);
  box-shadow: 2px 2px 4px rgba(123, 158, 137, 0.1);
  appearance: none;
  position: relative;
}

/* 添加自定义下拉箭头 */
.theme-switcher select::after {
  content: '▼';
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%) rotate(10deg);
  font-size: 0.8rem;
  color: #7B9E89;
  pointer-events: none;
}

.theme-switcher select:hover {
  border-color: #7B9E89;
  background: linear-gradient(135deg, #FEFCF7 0%, rgba(183, 223, 186, 0.2) 100%);
  transform: rotate(1deg) scale(1.05);
  box-shadow: 3px 3px 6px rgba(123, 158, 137, 0.2);
}

.theme-switcher select:focus {
  outline: none;
  border-color: #7B9E89;
  background: linear-gradient(135deg, #FEFCF7 0%, rgba(255, 182, 193, 0.1) 100%);
  transform: rotate(0deg) scale(1.05);
  box-shadow:
    4px 4px 8px rgba(123, 158, 137, 0.3),
    0 0 0 4px rgba(183, 223, 186, 0.3);
}

/* 手绘风格的选项样式 */
.theme-switcher select option {
  background: #FEFCF7;
  color: #4A4038;
  font-family: 'Comic Sans MS', 'Chalkduster', 'Marker Felt', cursive, sans-serif;
  font-weight: 600;
  padding: 0.5rem;
}

/* 添加装饰性下划线 */
.theme-switcher::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 10%;
  right: 10%;
  height: 2px;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(183, 223, 186, 0.6) 30%,
      rgba(255, 182, 193, 0.6) 70%,
      transparent 100%);
  border-radius: 50px;
  transform: rotate(2deg);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.theme-switcher:hover::after {
  opacity: 1;
}
</style>
