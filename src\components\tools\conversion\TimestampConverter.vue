<template>
  <ToolLayout
    title="时间戳转换器"
    description="Unix时间戳与人类可读时间的双向转换工具"
    layout="custom"
    left-icon="⏰"
    right-icon="🕐"
    :show-icon="true"
    :status-message="statusMessage"
    :status-type="statusType"
  >
    <template #icon>
      <svg width="60" height="60" viewBox="0 0 60 60" class="clock-icon">
        <circle cx="30" cy="30" r="25" />
        <line x1="30" y1="30" x2="30" y2="15" />
        <line x1="30" y1="30" x2="40" y2="30" />
        <circle cx="30" cy="30" r="2" fill="#eab308" />
      </svg>
    </template>

    <template #status>
      <div class="current-time-display">
        <div class="relative z-10">
          <h2 class="text-xl font-bold mb-2">🕐 当前时间</h2>
          <div class="text-2xl font-mono mb-1">{{ currentTimestamp }}</div>
          <div class="text-lg">{{ currentDateTime }}</div>
        </div>
      </div>
    </template>

    <!-- 主要转换区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 时间戳转日期 -->
      <div class="sketch-border bg-white p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <span class="mr-2">📅</span>
          时间戳转日期
        </h2>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">输入时间戳：</label>
            <input 
              v-model="timestampInput" 
              type="text" 
              class="input-sketch w-full font-mono" 
              placeholder="例如：1640995200 或 1640995200000"
              @input="handleTimestampInput"
            >
            <div class="text-xs text-gray-500 mt-1">
              支持10位（秒）或13位（毫秒）时间戳
            </div>
          </div>
          
          <div class="flex flex-wrap gap-2">
            <button @click="convertTimestampToDate" class="btn-sketch">
              <span class="mr-1">🔄</span>
              转换为日期
            </button>
            <button @click="useCurrentTimestamp" class="btn-sketch btn-blue">
              <span class="mr-1">⏰</span>
              使用当前时间戳
            </button>
          </div>
          
          <ResultDisplay
            v-if="timestampResults.length > 0"
            :results="timestampResults"
            :allow-copy="true"
            @copy="handleCopy"
          />
        </div>
      </div>
      
      <!-- 日期转时间戳 -->
      <div class="sketch-border bg-white p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <span class="mr-2">🔢</span>
          日期转时间戳
        </h2>
        
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">日期：</label>
              <input 
                v-model="dateInput" 
                type="date" 
                class="input-sketch w-full"
              >
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">时间：</label>
              <input 
                v-model="timeInput" 
                type="time" 
                class="input-sketch w-full"
                step="1"
              >
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">时区：</label>
            <select v-model="timezoneSelect" class="timezone-select w-full">
              <option value="local">本地时区</option>
              <option value="UTC">UTC (协调世界时)</option>
              <option value="Asia/Shanghai">Asia/Shanghai (北京时间)</option>
              <option value="America/New_York">America/New_York (纽约时间)</option>
              <option value="Europe/London">Europe/London (伦敦时间)</option>
              <option value="Asia/Tokyo">Asia/Tokyo (东京时间)</option>
            </select>
          </div>
          
          <div class="flex flex-wrap gap-2">
            <button @click="convertDateToTimestamp" class="btn-sketch">
              <span class="mr-1">🔄</span>
              转换为时间戳
            </button>
            <button @click="useCurrentDate" class="btn-sketch btn-green">
              <span class="mr-1">📅</span>
              使用当前日期
            </button>
          </div>
          
          <ResultDisplay
            v-if="dateResults.length > 0"
            :results="dateResults"
            :allow-copy="true"
            @copy="handleCopy"
          />
        </div>
      </div>
    </div>

    <template #actions>
      <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
        <div 
          v-for="action in quickActions" 
          :key="action.label"
          class="quick-action" 
          @click="useQuickAction(action.offset)"
        >
          <div class="text-sm font-medium">{{ action.label }}</div>
          <div class="text-xs text-gray-500">{{ action.desc }}</div>
        </div>
      </div>
    </template>

    <template #extra>
      <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
        <span class="mr-2">📋</span>
        常用时间格式
      </h3>
      
      <ResultDisplay
        :results="commonFormats"
        :allow-copy="true"
        size="small"
        @copy="handleCopy"
      />
    </template>
  </ToolLayout>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import ToolLayout from '@/components/common/ToolLayout.vue'
import ResultDisplay from '@/components/common/ResultDisplay.vue'
import { useTimestamp } from '@/composables/useTimestamp.js'
import { useCopy } from '@/composables/useCopy.js'

// 使用 composables
const {
  currentTimestamp,
  currentDateTime,
  timestampToDate,
  dateToTimestamp,
  formatDateTime,
  getCommonFormats,
  getQuickTimestamp
} = useTimestamp()

const { copyStatus, copyMessage } = useCopy()

// 响应式数据
const timestampInput = ref('')
const dateInput = ref('')
const timeInput = ref('')
const timezoneSelect = ref('local')

const timestampResults = ref([])
const dateResults = ref([])

const statusMessage = ref('')
const statusType = ref('info')

// 快捷操作配置
const quickActions = [
  { label: '现在', desc: '当前时间', offset: 0 },
  { label: '1小时后', desc: '+1h', offset: 3600 },
  { label: '明天', desc: '+1d', offset: 86400 },
  { label: '下周', desc: '+7d', offset: 604800 },
  { label: '下月', desc: '+30d', offset: 2592000 },
  { label: '明年', desc: '+365d', offset: 31536000 }
]

// 常用格式
const commonFormats = computed(() => {
  return getCommonFormats().map(format => ({
    label: format.label,
    value: format.value,
    type: 'text'
  }))
})

// 设置当前日期时间
const setCurrentDateTime = () => {
  const now = new Date()
  
  // 设置日期
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  dateInput.value = `${year}-${month}-${day}`
  
  // 设置时间
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  timeInput.value = `${hours}:${minutes}:${seconds}`
}

// 初始化当前日期时间
setCurrentDateTime()

// 时间戳输入处理（防抖）
let debounceTimer = null
const handleTimestampInput = () => {
  clearTimeout(debounceTimer)
  debounceTimer = setTimeout(() => {
    if (timestampInput.value.trim()) {
      convertTimestampToDate(true)
    } else {
      timestampResults.value = []
    }
  }, 300)
}

// 转换时间戳为日期
const convertTimestampToDate = (silent = false) => {
  const input = timestampInput.value.trim()
  if (!input) {
    timestampResults.value = []
    return
  }
  
  const result = timestampToDate(input)
  
  if (result.success) {
    timestampResults.value = result.formats
    if (!silent) {
      showStatus('时间戳转换成功！', 'success')
    }
  } else {
    timestampResults.value = []
    if (!silent) {
      showStatus(`转换失败: ${result.error}`, 'error')
    }
  }
}

// 转换日期为时间戳
const convertDateToTimestamp = () => {
  const result = dateToTimestamp(dateInput.value, timeInput.value, timezoneSelect.value)
  
  if (result.success) {
    dateResults.value = result.formats
    showStatus('日期转换成功！', 'success')
  } else {
    dateResults.value = []
    showStatus(`转换失败: ${result.error}`, 'error')
  }
}

// 使用当前时间戳
const useCurrentTimestamp = () => {
  timestampInput.value = currentTimestamp.value.toString()
  convertTimestampToDate()
}

// 使用当前日期
const useCurrentDate = () => {
  setCurrentDateTime()
}

// 快捷操作
const useQuickAction = (offset) => {
  const timestamp = getQuickTimestamp(offset)
  timestampInput.value = timestamp.toString()
  convertTimestampToDate()
}

// 复制处理
const handleCopy = (text) => {
  showStatus('已复制到剪贴板！', 'success')
}

// 显示状态信息
const showStatus = (message, type) => {
  statusMessage.value = message
  statusType.value = type
  
  setTimeout(() => {
    statusMessage.value = ''
  }, 3000)
}
</script>
